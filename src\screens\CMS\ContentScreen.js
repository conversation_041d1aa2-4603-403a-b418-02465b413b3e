import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Platform,
  ScrollView,
  Linking,
  Modal,
  Alert,
  Share,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import BackIcon from '../../components/BackIcon';
import RefreshIcon from '../../components/RefreshIcon';
import { clearWebViewSession, disposeWebView } from '../../utils/WebViewUtils';
import { useTheme } from '../../contexts/ThemeContext';
import ThemeTransitionWrapper from '../../components/ThemeTransitionWrapper';
import Svg, { Path, Rect } from 'react-native-svg';
import { checkDemoMode, getDemoCourseContent } from '../../utils/DemoData';

// Content type icons
const FileIcon = ({ color = '#000', size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M6 2C4.89543 2 4 2.89543 4 4V20C4 21.1046 4.89543 22 6 22H18C19.1046 22 20 21.1046 20 20V8.82843C20 8.29799 19.7893 7.78929 19.4142 7.41421L14.5858 2.58579C14.2107 2.21071 13.702 2 13.1716 2H6Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <Path
      d="M14 2V8H20"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const FolderIcon = ({ color = '#000', size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Path
      d="M3 6C3 4.89543 3.89543 4 5 4H9L11 6H19C20.1046 6 21 6.89543 21 8V18C21 19.1046 20.1046 20 19 20H5C3.89543 20 3 19.1046 3 18V6Z"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const VODIcon = ({ color = '#000', size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill="none">
    <Rect
      x="3"
      y="6"
      width="13"
      height="12"
      rx="2"
      stroke={color}
      strokeWidth="2"
      strokeLinejoin="round"
    />
    <Path
      d="M16 8L21 6V18L16 16V8Z"
      stroke={color}
      strokeWidth="2"
      strokeLinejoin="round"
      strokeLinecap="round"
    />
  </Svg>
);

const ShareIcon = ({ color = '#000', size = 24 }) => (
  <Svg width={size} height={size} viewBox="0 0 24 24" fill={color}>
    <Path d="M10 5v4c-5.33 0-8 3.33-8 9 1.5-3 4-5 8-5v4l9-6-9-6z"/>
  </Svg>
);

const ContentScreen = ({ navigation, route }) => {

  // Get the course data from route params - handle both course object and individual params
  const { course, courseCode, courseName, isDemoMode } = route.params || {};

  // Create course object if individual params are provided
  const courseData = course || {
    code: courseCode,
    name: courseName
  };
  
  // Theme context
  const { theme, currentThemeName } = useTheme();
  const safeCurrentThemeName = currentThemeName || 'dark';

  // State management
  const [refreshRotation] = useState(new Animated.Value(0));
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [contentUrl, setContentUrl] = useState('');
  const [courseDescription, setCourseDescription] = useState('');
  const [announcements, setAnnouncements] = useState([]); // Add announcements state
  const [weeklyContent, setWeeklyContent] = useState([]);
  const [selectedContent, setSelectedContent] = useState(null);
  const [showContentModal, setShowContentModal] = useState(false);
  const [hasCachedData, setHasCachedData] = useState(false);
  const [isBackgroundRefreshing, setIsBackgroundRefreshing] = useState(false);

  // Note: Fallback mode is tracked via selectedContent.isFallback property

  // Filter state
  const [availableFilters, setAvailableFilters] = useState([]);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [filteredWeeklyContent, setFilteredWeeklyContent] = useState([]);

  // Refs
  const webViewRef = useRef(null);
  const modalWebViewRef = useRef(null);

  // Refs for tracking background operations and component mount state
  const isMountedRef = useRef(true);
  const activeTimeoutsRef = useRef(new Set());

  // Comprehensive function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 ContentScreen: Killing all background operations...');

    // Mark component as unmounted to prevent state updates
    isMountedRef.current = false;

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();

    // Stop all animations
    refreshRotation.stopAnimation();

    // Reset all loading states to prevent cache corruption
    setIsLoading(false);
    setIsBackgroundRefreshing(false);

    // Dispose of WebViews
    await disposeWebView(webViewRef, 'content-webview');
    await disposeWebView(modalWebViewRef, 'modal-webview');

    console.log('✅ ContentScreen: All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ ContentScreen: Prevented ${stateName} state update after unmount`);
    }
  };

  // Safe timeout wrapper that tracks timeouts for cleanup
  const safeSetTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      activeTimeoutsRef.current.delete(timeoutId);
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  // Disable inspector on component mount
  useEffect(() => {
    // Disable React Native inspector and element inspector
    if (__DEV__) {
      try {
        // Disable the element inspector
        const DevSettings = require('react-native/Libraries/Utilities/DevSettings');
        if (DevSettings && DevSettings.setIsInspectorShown) {
          DevSettings.setIsInspectorShown(false);
        }

        // Also try to disable the inspector through global
        if (global.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
          global.__REACT_DEVTOOLS_GLOBAL_HOOK__.isDisabled = true;
        }
      } catch (error) {
        console.log('Could not disable inspector:', error);
      }
    }
  }, []);

  // Handle navigation focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 ContentScreen: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 ContentScreen: Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Kill background operations when navigating away completely
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 ContentScreen: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  // Load course content with cache-first approach
  useEffect(() => {
    const loadCourseContentWithCache = async () => {
      try {
        console.log('🚀 ContentScreen: Loading course content for', courseData?.name);

        // Check if we're in demo mode or if demo mode was passed via route params
        const isDemoModeDetected = await checkDemoMode() || route.params?.isDemoMode;
        if (isDemoModeDetected) {
          console.log('🎭 Demo mode detected - loading demo course content');

          // Get demo content for this course
          const demoContent = getDemoCourseContent(route.params?.courseCode || courseData?.code);

          if (demoContent) {
            // Simulate loading delay
            setTimeout(() => {
              setCourseDescription(demoContent.courseDescription);
              setAnnouncements(demoContent.announcements || []);
              setWeeklyContent(demoContent.weeks || []);
              
              // Set available filters from demo data
              setAvailableFilters(demoContent.filterTypes || []);
              
              setIsLoading(false);
              console.log('🎭 Demo course content loaded:', {
                announcements: demoContent.announcements?.length || 0,
                weeks: demoContent.weeks?.length || 0,
                filters: demoContent.filterTypes?.length || 0
              });
            }, 200);
          } else {
            // No demo content available for this course
            setCourseDescription('This is a demo course. Content is not available in demo mode.');
            setAnnouncements([]);
            setWeeklyContent([]);
            setAvailableFilters([]);
            setIsLoading(false);
          }
          return;
        }

        // Step 1: Try to load from cache first
        const hasCached = await loadFromCache();

        if (hasCached) {
          console.log('✅ ContentScreen: Using cached data, starting background refresh...');
          setIsLoading(false); // Show cached data immediately
          setIsBackgroundRefreshing(true); // Start background refresh
          startRotationAnimation(); // Start rotation animation for background refresh

          // Start background refresh
          await loadFreshContent(true);
        } else {
          console.log('📭 ContentScreen: No cached data, loading fresh content...');
          // No cache, load normally with loading state
          await loadFreshContent(false);
        }
      } catch (error) {
        console.log('❌ ContentScreen: Error in loadCourseContentWithCache:', error);
        setIsLoading(false);
      }
    };

    const loadFreshContent = async (isBackgroundRefresh = false) => {
      try {
        // Get stored credentials
        const storedUsername = await AsyncStorage.getItem('guc_username');
        const storedPassword = await AsyncStorage.getItem('guc_password');

        if (storedUsername && storedPassword && courseData?.id && courseData?.sid) {
          // Encode credentials to handle special characters like % in passwords
          const encodedUsername = encodeURIComponent(storedUsername);
          const encodedPassword = encodeURIComponent(storedPassword);

          // Create URL for the specific course content using id and sid
          const courseContentUrl = `https://${encodedUsername}:${encodedPassword}@cms.guc.edu.eg/apps/student/CourseViewStn.aspx?id=${courseData.id}&sid=${courseData.sid}`;
          setContentUrl(courseContentUrl);
          console.log('🌐 ContentScreen: Loading content URL for:', courseData.code, isBackgroundRefresh ? '(background)' : '(foreground)');
        } else {
          console.log('❌ ContentScreen: Missing course data:', { code: courseData?.code, id: courseData?.id, sid: courseData?.sid });
          setIsLoading(false);
          setIsBackgroundRefreshing(false);
          stopRotationAnimation(); // Stop rotation on missing data
        }
      } catch (error) {
        console.log('❌ ContentScreen: Error loading fresh content:', error);
        setIsLoading(false);
        setIsBackgroundRefreshing(false);
        stopRotationAnimation(); // Stop rotation on error
      }
    };

    if (courseData) {
      loadCourseContentWithCache();
    }

    // Cleanup function
    return () => {
      console.log('🧹 ContentScreen: Component unmounting - cleaning up...');
      killAllBackgroundOperations();
    };
  }, [course]);

  // Update filters when weekly content changes
  useEffect(() => {
    const filters = extractAvailableFilters(weeklyContent);
    console.log('🏷️ Extracted filters:', filters);
    console.log('📋 Weekly content sample:', weeklyContent.slice(0, 1));
    setAvailableFilters(filters);
  }, [weeklyContent]);

  // Update filtered content when filters or content changes
  useEffect(() => {
    const filtered = filterWeeklyContent(weeklyContent, selectedFilters);
    setFilteredWeeklyContent(filtered);
  }, [weeklyContent, selectedFilters]);

  // Rotation animation functions
  const startRotationAnimation = () => {
    refreshRotation.setValue(0);
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopRotationAnimation = () => {
    Animated.timing(refreshRotation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  // Start/stop rotation animation based on loading states
  useEffect(() => {
    if (isLoading || isRefreshing) {
      startRotationAnimation();
    } else {
      stopRotationAnimation();
    }
  }, [isLoading, isRefreshing]);

  // Handle WebView load and inject data extraction JavaScript
  const handleWebViewLoad = () => {
    console.log('Content WebView loaded, injecting JavaScript...');
    if (webViewRef.current) {
      const jsCode = `
        (function() {
          // Add timeout to prevent infinite execution
          const startTime = Date.now();
          const TIMEOUT_MS = 10000; // 10 seconds timeout

          try {
            console.log('Extracting course content data...');

            // Check if we're taking too long
            if (Date.now() - startTime > TIMEOUT_MS) {
              throw new Error('Content extraction timeout');
            }

            // Extract course description from ContentPlaceHolderright_ContentPlaceHoldercontent_desc
            const descElement = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_desc');
            let description = '';
            if (descElement) {
              description = descElement.innerHTML;
              console.log('Found description element');
            }

            // Extract weekly content based on the actual HTML structure
            const weeks = [];

            // Look for week containers with class "weeksdata"
            const weekElements = document.querySelectorAll('.weeksdata');

            weekElements.forEach((weekElement, index) => {
              // Check timeout in loop
              if (Date.now() - startTime > TIMEOUT_MS) {
                throw new Error('Content extraction timeout in week loop');
              }

              // Extract week title from h2.text-big
              const titleElement = weekElement.querySelector('h2.text-big');
              if (!titleElement) return;

              const weekTitle = titleElement.textContent.trim();

              const week = {
                title: weekTitle,
                content: []
              };

              // Extract content items from .card.mb-4 within this week
              const contentCards = weekElement.querySelectorAll('.card.mb-4');

              contentCards.forEach(card => {
                try {
                  // Check timeout in content loop
                  if (Date.now() - startTime > TIMEOUT_MS) {
                    throw new Error('Content extraction timeout in content loop');
                  }

                  // Get the content div that contains both title and tag
                  const contentDiv = card.querySelector('[id^="content"]');
                  if (contentDiv) {
                    // Get the content title (strong text)
                    const contentTitleElement = contentDiv.querySelector('strong');
                    const contentTitle = contentTitleElement ? contentTitleElement.textContent.trim() : '';

                    // Extract the tag - it's the text after </strong> but before </div>
                    let contentTag = '';
                    try {
                      const fullText = contentDiv.innerHTML || '';
                      const tagMatch = fullText.match(/<\\/strong>\\s*\\(([^)]+)\\)/);
                      contentTag = tagMatch ? tagMatch[1].trim() : '';
                    } catch (tagError) {
                      console.log('Tag extraction error:', tagError);
                      contentTag = '';
                    }

                    // Minimal debug logging to prevent console spam
                    if (contentTitle && index === 0) {
                      console.log('Sample content extraction:', {
                        title: contentTitle.substring(0, 50) + '...',
                        tag: contentTag
                      });
                    }

                    // Look for download links
                    const downloadLink = card.querySelector('a[download]');
                    if (downloadLink && contentTitle) {
                      week.content.push({
                        type: 'link',
                        text: contentTitle,
                        tag: contentTag,
                        url: downloadLink.href
                      });
                    } else if (contentTitle) {
                      // If no download link, add as text
                      week.content.push({
                        type: 'text',
                        text: contentTitle,
                        tag: contentTag
                      });
                    }
                  }
                } catch (cardError) {
                  console.log('Card processing error:', cardError);
                }
              });

              // Only add week if it has content
              if (week.content.length > 0) {
                weeks.push(week);
              }
            });

            console.log('Extracted weeks:', weeks.length);
            console.log('Description length:', description.length);

            // Send data back to React Native
            if (window.ReactNativeWebView) {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'content_data_extracted',
                description: description,
                weeks: weeks,
                success: true
              }));
            }

          } catch (error) {
            console.error('Error extracting content data:', error);

            // Always send a response to prevent hanging
            try {
              if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'content_data_extracted',
                  error: error.message || 'Unknown error',
                  success: false,
                  description: '',
                  weeks: []
                }));
              }
            } catch (postError) {
              console.error('Error posting message:', postError);
            }
          }

          // Fallback timeout mechanism
          setTimeout(function() {
            try {
              if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'content_data_extracted',
                  error: 'Extraction timeout - fallback triggered',
                  success: false,
                  description: '',
                  weeks: []
                }));
              }
            } catch (fallbackError) {
              console.error('Fallback error:', fallbackError);
            }
          }, 15000); // 15 second fallback
        })();
      `;

      webViewRef.current.injectJavaScript(jsCode);
    }
  };

  // Handle messages from WebView
  const handleWebViewMessage = async (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('Content WebView message:', data.type);

      switch (data.type) {
        case 'content_data_extracted':
          if (data.success) {
            console.log('✅ ContentScreen: Content data extracted successfully');
            console.log('📄 Description:', data.description ? 'Found' : 'Not found');
            console.log('📅 Weeks:', data.weeks.length);

            // Use cache comparison and update system
            const hasChanges = compareAndUpdateData(data.description || '', data.weeks || []);

            if (isBackgroundRefreshing) {
              console.log('🔄 ContentScreen: Background refresh completed. Data changed:', hasChanges);
              setIsBackgroundRefreshing(false);
              stopRotationAnimation(); // Stop rotation when background refresh completes
            }
          } else {
            console.log('❌ ContentScreen: Content data extraction failed:', data.error);
          }

          // Stop loading states
          setIsLoading(false);
          setIsRefreshing(false);
          setIsBackgroundRefreshing(false);
          stopRotationAnimation(); // Stop rotation on completion
          break;

        default:
          console.log('Unknown message type:', data.type);
          break;
      }
    } catch (error) {
      console.log('❌ ContentScreen: Error parsing WebView message:', error);
      setIsLoading(false);
      setIsRefreshing(false);
      setIsBackgroundRefreshing(false);
      stopRotationAnimation(); // Stop rotation on error
    }
  };

  // Handle WebView errors
  const handleWebViewError = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.log('❌ ContentScreen: WebView error:', nativeEvent);
    setIsLoading(false);
    setIsRefreshing(false);
    setIsBackgroundRefreshing(false);
    stopRotationAnimation(); // Stop rotation on WebView error
  };

  // Handle refresh button press
  const handleRefresh = () => {
    console.log('🔄 ContentScreen: Refresh button pressed');
    setIsRefreshing(true);
    startRotationAnimation();

    // Don't clear existing data - keep it visible during refresh (cache-first approach)
    // The data will be updated when new data arrives

    // Reload WebView to fetch fresh data
    if (webViewRef.current && contentUrl) {
      webViewRef.current.reload();
    }
  };

  // Determine content type based on URL or filename
  const getContentType = (url, text) => {
    const lowerUrl = url.toLowerCase();
    const lowerText = text.toLowerCase();

    // Check for zip files first (should be treated as folders for download)
    if (lowerUrl.includes('.zip') || lowerText.includes('.zip')) {
      return 'zip';
    }

    // Check for folder/directory content
    if (lowerUrl.includes('folder') || lowerUrl.includes('directory') ||
        lowerText.includes('folder') || lowerText.includes('week') || lowerText.includes('chapter')) {
      return 'folder';
    }

    // Check for document files first (PDF, DOC, etc.) - these should be file icons
    if (lowerUrl.includes('.pdf') || lowerUrl.includes('.doc') || lowerUrl.includes('.docx') ||
        lowerUrl.includes('.txt') || lowerUrl.includes('.rtf') || lowerUrl.includes('.ppt') ||
        lowerUrl.includes('.pptx') || lowerUrl.includes('.xls') || lowerUrl.includes('.xlsx')) {
      return 'file';
    }

    // Check for video/audio/image content (VOD) - only for actual media files
    if (lowerUrl.includes('.mp4') || lowerUrl.includes('.avi') || lowerUrl.includes('.mov') ||
        lowerUrl.includes('.wmv') || lowerUrl.includes('.flv') || lowerUrl.includes('.webm') ||
        lowerUrl.includes('.mp3') || lowerUrl.includes('.wav') || lowerUrl.includes('.aac') ||
        lowerUrl.includes('.jpg') || lowerUrl.includes('.jpeg') || lowerUrl.includes('.png') ||
        lowerUrl.includes('.gif') || lowerUrl.includes('.bmp') || lowerUrl.includes('.svg') ||
        lowerUrl.includes('video') || lowerUrl.includes('vod') || lowerUrl.includes('youtube')) {
      return 'vod';
    }

    // Check text-based indicators for VOD (but only if no document extension found)
    if (lowerText.includes('video') || lowerText.includes('recording') || lowerText.includes('audio') ||
        lowerText.includes('image')) {
      return 'vod';
    }

    // Default to file for everything else (including "lecture" which could be PDF)
    return 'file';
  };

  // Clean file name by removing count prefix (e.g., "2 - Tutorial 2" -> "Tutorial 2")
  const cleanFileName = (text) => {
    // Remove patterns like "1 - ", "2 - ", "10 - ", etc. from the beginning
    return text.replace(/^\d+\s*-\s*/, '').trim();
  };

  // Extract all unique tags from weekly content - updated for demo data structure
  const extractAvailableFilters = (content) => {
    const filters = new Set();
    content.forEach(week => {
      // Handle demo data structure with materials array
      if (week.materials) {
        week.materials.forEach(material => {
          if (material.type && material.type.trim()) {
            filters.add(material.type.trim());
          }
        });
      }
      // Handle original data structure with content array
      else if (week.content) {
        week.content.forEach(item => {
          if (item.tag && item.tag.trim()) {
            filters.add(item.tag.trim());
          }
        });
      }
    });
    // Sort alphabetically
    return Array.from(filters).sort((a, b) => a.localeCompare(b));
  };

  // Filter weekly content based on selected filters - updated for demo data structure
  const filterWeeklyContent = (content, filters) => {
    if (filters.length === 0) {
      return content; // Show all content if no filters selected
    }

    return content.map(week => {
      // Handle demo data structure with materials array
      if (week.materials) {
        return {
          ...week,
          materials: week.materials.filter(material => {
            return filters.includes(material.type);
          })
        };
      }
      // Handle original data structure with content array
      else if (week.content) {
        return {
          ...week,
          content: week.content.filter(item => {
            if (!item.tag || !item.tag.trim()) {
              return false; // Don't show items without tags when filtering
            }
            return filters.includes(item.tag.trim());
          })
        };
      }
      return week;
    }).filter(week => {
      // Remove weeks with no content after filtering
      if (week.materials) {
        return week.materials.length > 0;
      } else if (week.content) {
        return week.content.length > 0;
      }
      return true;
    });
  };

  // Handle filter toggle
  const toggleFilter = (filter) => {
    setSelectedFilters(prev => {
      if (prev.includes(filter)) {
        return prev.filter(f => f !== filter);
      } else {
        return [...prev, filter];
      }
    });
  };

  // Cache management functions
  const getCacheKey = () => {
    return `cms_content_${courseData?.code || 'unknown'}`;
  };

  const loadFromCache = async () => {
    try {
      const cacheKey = getCacheKey();
      const cachedData = await AsyncStorage.getItem(cacheKey);

      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        console.log('✅ ContentScreen: Loaded cached data for', courseData?.code);

        // Set cached data immediately
        setCourseDescription(parsedData.courseDescription || '');
        setWeeklyContent(parsedData.weeklyContent || []);
        setHasCachedData(true);

        return true;
      }

      console.log('📭 ContentScreen: No cached data found for', courseData?.code);
      return false;
    } catch (error) {
      console.log('❌ ContentScreen: Error loading cache:', error);
      return false;
    }
  };

  const saveToCache = async (courseDescription, weeklyContent) => {
    try {
      const cacheKey = getCacheKey();
      const dataToCache = {
        courseCode: courseData?.code,
        courseName: courseData?.name,
        courseDescription,
        weeklyContent,
        lastUpdated: new Date().toISOString()
      };

      await AsyncStorage.setItem(cacheKey, JSON.stringify(dataToCache));
      console.log('💾 ContentScreen: Cached data for', courseData?.code);
    } catch (error) {
      console.log('❌ ContentScreen: Error saving cache:', error);
    }
  };

  const compareAndUpdateData = (newCourseDescription, newWeeklyContent) => {
    let hasChanges = false;

    // Compare course description
    if (newCourseDescription !== courseDescription) {
      console.log('🔄 ContentScreen: Course description changed');
      setCourseDescription(newCourseDescription);
      hasChanges = true;
    }

    // Compare weekly content (simple length and structure comparison)
    if (JSON.stringify(newWeeklyContent) !== JSON.stringify(weeklyContent)) {
      console.log('🔄 ContentScreen: Weekly content changed');
      setWeeklyContent(newWeeklyContent);
      hasChanges = true;
    }

    if (hasChanges) {
      console.log('💾 ContentScreen: Saving updated data to cache');
      saveToCache(newCourseDescription, newWeeklyContent);
    } else {
      console.log('✅ ContentScreen: No changes detected, cache up to date');
    }

    return hasChanges;
  };

  // Download file function (unified approach for both iOS and Android)
  const downloadFile = async (url, fileName) => {
    try {
      // Get stored credentials
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        Alert.alert('Error', 'Authentication credentials not found.');
        return;
      }

      // Encode credentials to handle special characters like % in passwords
      const encodedUsername = encodeURIComponent(storedUsername);
      const encodedPassword = encodeURIComponent(storedPassword);

      // Remove existing credentials from URL and add properly encoded ones
      let authenticatedUrl = url;

      // Remove existing credentials if present (pattern: https://username:password@)
      authenticatedUrl = authenticatedUrl.replace(/https:\/\/[^@]+@/, 'https://');

      // Add properly encoded credentials
      authenticatedUrl = authenticatedUrl.replace('https://', `https://${encodedUsername}:${encodedPassword}@`);
      console.log('Authenticated URL for download:', authenticatedUrl);
      // Unified approach: Open in default browser for download (same as iOS Safari approach)
      Alert.alert(
        'Download File',
        `${fileName} will open in your browser for download.`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Open in Browser',
            onPress: async () => {
              try {
                const supported = await Linking.canOpenURL(authenticatedUrl);
                if (supported) {
                  await Linking.openURL(authenticatedUrl);
                } else {
                  Alert.alert('Error', 'Cannot open the download link.');
                }
              } catch (error) {
                console.error('Error opening URL:', error);
                Alert.alert('Error', 'Failed to open the download link.');
              }
            }
          }
        ]
      );

    } catch (error) {
      console.error('Download error:', error);
      Alert.alert('Download Failed', 'There was an error preparing the download. Please try again.');
    }
  };

  // Handle content item press - show in app or download
  const handleContentPress = async (url, text) => {
    const contentType = getContentType(url, text);

    console.log('Content pressed:', { url, text, contentType });

    // For zip files, download directly instead of opening in modal
    if (contentType === 'zip') {
      console.log('Handling zip file download');
      const cleanedFileName = cleanFileName(text);
      // Extract file extension from URL or use .zip as default
      const fileExtension = url.toLowerCase().includes('.zip') ? '.zip' : '.zip';
      const fileName = cleanedFileName.endsWith(fileExtension) ? cleanedFileName : `${cleanedFileName}${fileExtension}`;
      downloadFile(url, fileName);
      return;
    }

    // For other files, check if it's a PDF on Android and handle differently
    console.log('Handling regular file for in-app viewing');

    // Check if it's a PDF file on Android - open in browser instead of WebView
    if (Platform.OS === 'android' && url.toLowerCase().includes('.pdf')) {
      console.log('PDF detected on Android, opening in browser instead of WebView');
      // Use the same download function which opens in browser
      const cleanedFileName = cleanFileName(text);
      const fileName = cleanedFileName.endsWith('.pdf') ? cleanedFileName : `${cleanedFileName}.pdf`;
      downloadFile(url, fileName);
      return;
    }

    try {
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (storedUsername && storedPassword) {
        // Encode credentials to handle special characters like % in passwords
        const encodedUsername = encodeURIComponent(storedUsername);
        const encodedPassword = encodeURIComponent(storedPassword);

        // Remove existing credentials from URL and add properly encoded ones
        let authenticatedUrl = url;

        // Remove existing credentials if present (pattern: https://username:password@)
        authenticatedUrl = authenticatedUrl.replace(/https:\/\/[^@]+@/, 'https://');

        // Add properly encoded credentials
        authenticatedUrl = authenticatedUrl.replace('https://', `https://${encodedUsername}:${encodedPassword}@`);

        console.log('Opening in modal with authenticated URL:', authenticatedUrl);
        setSelectedContent({
          url: authenticatedUrl,
          text: cleanFileName(text),
          type: contentType
        });
        setShowContentModal(true);
      } else {
        Alert.alert('Error', 'Authentication credentials not found.');
      }
    } catch (error) {
      console.error('Error preparing content for viewing:', error);
      Alert.alert('Error', 'Failed to prepare content for viewing.');
    }
  };

  // Handle video fallback when direct link fails with 404
  const handleVideoFallback = async (originalUrl, contentText) => {
    try {
      console.log('🔄 Starting video fallback for:', contentText);

      // Get stored credentials
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (storedUsername && storedPassword && course?.id && course?.sid) {
        // Encode credentials to handle special characters like % in passwords
        const encodedUsername = encodeURIComponent(storedUsername);
        const encodedPassword = encodeURIComponent(storedPassword);

        // Create URL for the course page (same as the main content URL)
        const coursePageUrl = `https://${encodedUsername}:${encodedPassword}@cms.guc.edu.eg/apps/student/CourseViewStn.aspx?id=${course.id}&sid=${course.sid}`;

        console.log('🌐 Loading course page for video fallback');

        // Update the selected content to load the course page in fallback mode
        setSelectedContent({
          url: coursePageUrl,
          text: contentText,
          type: 'vod',
          originalUrl: originalUrl, // Keep track of the original URL
          isFallback: true
        });

        return true;
      } else {
        console.log('❌ Missing credentials or course data for fallback');
        return false;
      }
    } catch (error) {
      console.log('❌ Error in video fallback:', error);
      return false;
    }
  };

  // Handle share button press
  const handleShare = async () => {
    try {
      if (selectedContent?.url) {
        const result = await Share.share({
          message: `Check out this content: ${selectedContent.text}`,
          url: selectedContent.url,
          title: selectedContent.text || 'Shared Content',
        });

        if (result.action === Share.sharedAction) {
          if (result.activityType) {
            console.log('Content shared via:', result.activityType);
          } else {
            console.log('Content shared successfully');
          }
        } else if (result.action === Share.dismissedAction) {
          console.log('Share dialog dismissed');
        }
      } else {
        Alert.alert('Error', 'No content URL available to share.');
      }
    } catch (error) {
      console.error('Error sharing content:', error);
      Alert.alert('Error', 'Failed to open share menu.');
    }
  };

  // Handle link clicks - open in native browser (for external links)
  const handleLinkPress = async (url) => {
    try {
      const supported = await Linking.canOpenURL(url);
      if (supported) {
        await Linking.openURL(url);
      } else {
        console.log("Don't know how to open URI: " + url);
      }
    } catch (error) {
      console.log('Error opening link:', error);
    }
  };

  // Decode HTML entities
  const decodeHtmlEntities = (text) => {
    const entities = {
      '&nbsp;': ' ',
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'",
      '&apos;': "'",
    };

    return text.replace(/&[^;]+;/g, (entity) => {
      return entities[entity] || entity;
    });
  };

  // Render description content with clickable links
  const renderDescriptionContent = (htmlContent) => {
    if (!htmlContent) return null;

    // Extract links from HTML
    const linkRegex = /<a[^>]*href="([^"]*)"[^>]*>(.*?)<\/a>/gi;
    const parts = [];
    let lastIndex = 0;
    let match;

    // Process HTML content to extract links

    while ((match = linkRegex.exec(htmlContent)) !== null) {
      const [fullMatch, url, linkText] = match;
      const beforeLink = htmlContent.substring(lastIndex, match.index);

      // Add text before link
      if (beforeLink) {
        const cleanText = decodeHtmlEntities(beforeLink.replace(/<[^>]*>/g, '')).trim();
        if (cleanText) {
          parts.push(
            <Text key={`text-${parts.length}`} style={styles.descriptionText}>
              {cleanText}
            </Text>
          );
        }
      }

      // Add clickable link
      parts.push(
        <TouchableOpacity
          key={`link-${parts.length}`}
          style={styles.descriptionLinkContainer}
          onPress={() => handleLinkPress(url)}
        >
          <Text style={styles.descriptionLinkText}>
            {decodeHtmlEntities(linkText.replace(/<[^>]*>/g, '')).trim()}
          </Text>
        </TouchableOpacity>
      );

      lastIndex = match.index + fullMatch.length;
    }

    // Add remaining text after last link
    const remainingText = htmlContent.substring(lastIndex);
    if (remainingText) {
      const cleanText = decodeHtmlEntities(remainingText.replace(/<[^>]*>/g, '')).trim();
      if (cleanText) {
        parts.push(
          <Text key={`text-${parts.length}`} style={styles.descriptionText}>
            {cleanText}
          </Text>
        );
      }
    }

    // If no links found, just show clean text
    if (parts.length === 0) {
      const cleanText = decodeHtmlEntities(htmlContent.replace(/<[^>]*>/g, '')).trim();
      return (
        <Text style={styles.descriptionText}>
          {cleanText}
        </Text>
      );
    }

    return <View>{parts}</View>;
  };

  // Get content icon for demo materials based on type
  const getDemoContentIcon = (material) => {
    const type = material.type?.toLowerCase() || '';
    const title = material.title?.toLowerCase() || '';
    
    // Check for video/vod content
    if (type.includes('video') || title.includes('video') || title.includes('recording') || title.includes('lecture recording')) {
      return <VODIcon color={theme.colors.primary} size={20} />;
    }
    
    // Check for folder/project content
    if (type.includes('project') || type.includes('folder') || title.includes('project') || title.includes('starter code') || title.includes('examples')) {
      return <FolderIcon color={theme.colors.primary} size={20} />;
    }
    
    // Default to file icon for everything else (assignments, slides, etc.)
    return <FileIcon color={theme.colors.primary} size={20} />;
  };

  // Handle demo content press (show mock download)
  const handleDemoContentPress = async (material) => {
    console.log('🎭 Demo content pressed:', material.title);
    
    Alert.alert(
      'Demo Mode',
      `In demo mode, file "${material.title}" would be downloaded.\n\nFile details:\n• Type: ${material.type}\n• Size: ${material.size || 'Unknown'}\n• Upload Date: ${material.uploadDate || 'Unknown'}`,
      [
        { text: 'OK', style: 'default' }
      ]
    );
  };

  // Handle back button press
  const handleBack = () => {
    navigation.goBack();
  };

  // Create styles function that uses theme
  const styles = createStyles(theme, safeCurrentThemeName);

  return (
    <ThemeTransitionWrapper>
      <View style={styles.container}>
        <SafeAreaView style={styles.container}>
          {/* Back Button */}
          <View style={styles.backButtonContainer}>
            <TouchableOpacity style={styles.backButton} onPress={handleBack}>
              <BackIcon size={20} color={theme.colors.primary} strokeWidth={3} />
            </TouchableOpacity>
          </View>

          {/* Course Title */}
          <View style={styles.courseTitleContainer}>
            <Text style={styles.courseTitle}>{courseData?.code || 'Course'}</Text>
          </View>

          {/* Refresh Button */}
          <View style={styles.refreshButtonContainer}>
            <TouchableOpacity
              style={[
                styles.refreshButton,
                (isRefreshing || isBackgroundRefreshing) && styles.refreshButtonLoading
              ]}
              onPress={handleRefresh}
              disabled={isRefreshing || isBackgroundRefreshing}
            >
              <Animated.View
                style={{
                  transform: [{
                    rotate: refreshRotation.interpolate({
                      inputRange: [0, 1],
                      outputRange: ['0deg', '360deg']
                    })
                  }]
                }}
              >
                <RefreshIcon
                  size={24}
                  color={(isRefreshing || isBackgroundRefreshing) ? '#808080' : '#f1c40f'}
                  strokeWidth={3}
                />
              </Animated.View>
            </TouchableOpacity>
          </View>

          {/* Main Content */}
          <View style={styles.mainContent}>
            <ScrollView
              style={styles.scrollContainer}
              contentContainerStyle={styles.scrollContentContainer}
              showsVerticalScrollIndicator={false}
              bounces={false}
            >
              {isLoading ? (
                <View style={styles.loadingContainer}>
                  <Text style={styles.loadingText}>Loading course content...</Text>
                </View>
              ) : (
                <>
                  {/* Announcements Section */}
                  {announcements && announcements.length > 0 ? (
                    <View style={styles.descriptionContainer}>
                      <Text style={styles.descriptionTitle}>Announcements</Text>
                      <View style={styles.descriptionContent}>
                        {announcements.map((announcement, index) => (
                          <View key={announcement.id || index} style={[
                            styles.announcementItem,
                            announcement.isImportant && styles.announcementItemImportant
                          ]}>
                            <Text style={styles.announcementText}>
                              {announcement.content}
                            </Text>
                          </View>
                        ))}
                      </View>
                    </View>
                  ) : courseDescription && courseDescription.trim() ? (
                    // Fallback to courseDescription if no announcements
                    <View style={styles.descriptionContainer}>
                      <Text style={styles.descriptionTitle}>Announcements</Text>
                      <View style={styles.descriptionContent}>
                        {renderDescriptionContent(courseDescription)}
                      </View>
                    </View>
                  ) : (
                    // No announcements at all
                    <View style={styles.descriptionContainer}>
                      <Text style={styles.descriptionTitle}>Announcements</Text>
                      <View style={styles.descriptionContent}>
                        <Text style={styles.emptyAnnouncementText}>No announcements available</Text>
                      </View>
                    </View>
                  )}

                  {/* Filter Buttons - Only show if there are filters available */}
                  {availableFilters.length > 0 && (
                    <View style={styles.filtersContainer}>
                      <Text style={styles.filtersTitle}>Filter by Type:</Text>
                      <View style={styles.filtersButtonsContainer}>
                        {availableFilters.map((filter) => (
                          <TouchableOpacity
                            key={filter}
                            style={[
                              styles.filterButton,
                              selectedFilters.includes(filter) && styles.filterButtonSelected
                            ]}
                            onPress={() => toggleFilter(filter)}
                          >
                            <Text style={[
                              styles.filterButtonText,
                              selectedFilters.includes(filter) && styles.filterButtonTextSelected
                            ]}>
                              {filter}
                            </Text>
                          </TouchableOpacity>
                        ))}
                      </View>
                    </View>
                  )}

                  {/* Weekly Content - Use filtered content */}
                  {filteredWeeklyContent.length > 0 ? (
                    <View style={styles.weeklyContainer}>
                      {filteredWeeklyContent.map((week, weekIndex) => (
                        <View key={week.weekId || weekIndex} style={styles.weekContainer}>
                          <Text style={styles.weekTitle}>{week.weekTitle || week.title}</Text>
                          <View style={styles.weekContent}>
                            {/* Handle demo data structure with materials */}
                            {week.materials ? (
                              week.materials.map((material, itemIndex) => (
                                <View key={material.id || itemIndex} style={styles.contentItem}>
                                  <TouchableOpacity
                                    style={styles.linkContainer} // Use same style as real content
                                    onPress={() => handleDemoContentPress(material)}
                                  >
                                    <View style={styles.linkContent}>
                                      <View style={styles.linkIconContainer}>
                                        {getDemoContentIcon(material)}
                                      </View>
                                      <Text style={styles.linkText}>{material.title}</Text>
                                    </View>
                                  </TouchableOpacity>
                                </View>
                              ))
                            ) : (
                              /* Handle original data structure with content */
                              week.content.map((item, itemIndex) => (
                                <View key={itemIndex} style={styles.contentItem}>
                                  {item.type === 'link' ? (
                                    <TouchableOpacity
                                      style={styles.linkContainer}
                                      onPress={() => handleContentPress(item.url, item.text)}
                                    >
                                      <View style={styles.linkContent}>
                                        <View style={styles.linkIconContainer}>
                                          {getContentType(item.url, item.text) === 'vod' && (
                                            <VODIcon color={theme.colors.primary} size={20} />
                                          )}
                                          {(getContentType(item.url, item.text) === 'folder' || getContentType(item.url, item.text) === 'zip') && (
                                            <FolderIcon color={theme.colors.primary} size={20} />
                                          )}
                                          {getContentType(item.url, item.text) === 'file' && (
                                            <FileIcon color={theme.colors.primary} size={20} />
                                          )}
                                        </View>
                                        <Text style={styles.linkText}>{cleanFileName(item.text)}</Text>
                                      </View>
                                    </TouchableOpacity>
                                  ) : (
                                    <Text style={styles.contentText}>{cleanFileName(item.text)}</Text>
                                  )}
                                </View>
                              ))
                            )}
                          </View>
                        </View>
                      ))}
                    </View>
                  ) : !isLoading ? (
                    <View style={styles.noContentContainer}>
                      <Text style={styles.noContentText}>
                        No weekly content available for this course.
                      </Text>
                    </View>
                  ) : null}
                </>
              )}
            </ScrollView>

          </View>
        </SafeAreaView>
      </View>

      {/* Hidden WebView for data extraction - COMPLETELY outside main container */}
      {contentUrl && (
        <View style={{
          position: 'absolute',
          top: -10000,
          left: -10000,
          width: 1,
          height: 1,
          opacity: 0,
          zIndex: -9999
        }}>
          <WebView
            ref={webViewRef}
            source={{ uri: contentUrl }}
            style={{ width: 1, height: 1 }}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            mixedContentMode="compatibility"
            allowsInlineMediaPlayback={true}
            mediaPlaybackRequiresUserAction={false}
            startInLoadingState={true}
            scalesPageToFit={true}
            originWhitelist={['*']}
            onLoad={handleWebViewLoad}
            onMessage={handleWebViewMessage}
            onError={handleWebViewError}
          />
        </View>
      )}

      {/* Content Viewer Modal */}
      <Modal
        visible={showContentModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setShowContentModal(false)}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setShowContentModal(false)}
            >
              <Text style={styles.modalCloseText}>✕</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              {selectedContent?.text || 'Content'}
            </Text>
            {/* Share button - only show for files and images, not for videos */}
            {selectedContent && selectedContent.type !== 'vod' && !selectedContent.isFallback && (
              <TouchableOpacity
                style={styles.modalShareButton}
                onPress={handleShare}
              >
                <ShareIcon color={theme.colors.primary} size={24} />
              </TouchableOpacity>
            )}
          </View>

          <View style={styles.modalContent}>
            {selectedContent && (
              <WebView
                ref={modalWebViewRef}
                source={{ uri: selectedContent.url }}
                style={styles.modalWebView}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                startInLoadingState={true}
                scalesPageToFit={true}
                mixedContentMode="compatibility"
                allowsInlineMediaPlayback={true}
                mediaPlaybackRequiresUserAction={false}
                originWhitelist={['*']}
                allowFileAccess={true}
                allowFileAccessFromFileURLs={true}
                allowUniversalAccessFromFileURLs={true}
                onShouldStartLoadWithRequest={(request) => {
                  console.log('WebView load request:', request.url);

                  // Check if it's a PDF file that might cause download
                  if (request.url.toLowerCase().includes('.pdf')) {
                    console.log('PDF detected, allowing WebView to handle it');
                  }

                  // Allow all requests to load in WebView instead of external browser
                  return true;
                }}
                onFileDownload={(downloadEvent) => {
                  console.log('WebView trying to download file:', downloadEvent.nativeEvent);
                  // Prevent automatic downloads
                  return false;
                }}
                onError={async (syntheticEvent) => {
                  const { nativeEvent } = syntheticEvent;
                  console.log('Modal WebView error:', nativeEvent);

                  // Check if this is a 404 error for video content and not already in fallback mode
                  const is404Error = nativeEvent.description && (
                    nativeEvent.description.includes('404') ||
                    nativeEvent.description.includes('Not Found') ||
                    nativeEvent.code === -1003 // iOS error code for "cannot find host"
                  );

                  const isVideoContent = selectedContent?.type === 'vod';
                  const isNotAlreadyFallback = !selectedContent?.isFallback;

                  if (is404Error && isVideoContent && isNotAlreadyFallback) {
                    console.log('🚨 404 error detected for video content, attempting fallback...');

                    const fallbackSuccess = await handleVideoFallback(
                      selectedContent.url,
                      selectedContent.text
                    );

                    if (!fallbackSuccess) {
                      Alert.alert(
                        'Video Unavailable',
                        'This video content could not be loaded. Please try again later.',
                        [{ text: 'OK', style: 'default' }]
                      );
                    }
                    return; // Don't show other error dialogs
                  }

                  // If WebView can't load PDF, show option to open externally
                  if (selectedContent.url.toLowerCase().includes('.pdf')) {
                    Alert.alert(
                      'PDF Viewer',
                      'This PDF cannot be displayed in the app. Would you like to open it in an external PDF viewer?',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        {
                          text: 'Open Externally',
                          onPress: async () => {
                            try {
                              const supported = await Linking.canOpenURL(selectedContent.url);
                              if (supported) {
                                await Linking.openURL(selectedContent.url);
                              } else {
                                Alert.alert('Error', 'No PDF viewer app found.');
                              }
                            } catch (error) {
                              console.error('Error opening PDF externally:', error);
                              Alert.alert('Error', 'Failed to open PDF.');
                            }
                          }
                        }
                      ]
                    );
                  }
                }}
                onLoad={() => {
                  console.log('Modal WebView loaded successfully');

                  // If this is fallback mode, inject JavaScript to handle video extraction
                  if (selectedContent?.isFallback && modalWebViewRef.current) {
                    console.log('🔧 Injecting fallback JavaScript for video extraction...');

                    // Inject JavaScript to find and click the Watch Video button
                    const fallbackJS = `
                      (function() {
                        console.log('📋 Course page loaded in fallback mode, searching for Watch Video button...');

                        try {
                          // Extract the video filename from the original URL
                          const originalUrl = '${selectedContent.originalUrl}';
                          console.log('Looking for video with URL:', originalUrl);

                          // Extract just the filename from the full URL (e.g., "GUC_14_55_12534_2021-12-13T13_10_01.mp4")
                          const urlParts = originalUrl.split('/');
                          const videoFileName = urlParts[urlParts.length - 1];
                          console.log('Extracted video filename:', videoFileName);

                          // Find all Watch Video buttons
                          const watchVideoButtons = document.querySelectorAll('input.vodbutton[value="Watch Video"]');
                          console.log('Found', watchVideoButtons.length, 'Watch Video buttons');

                          let targetButton = null;

                          // Look for the button with matching data-url
                          for (let button of watchVideoButtons) {
                            const buttonUrl = button.getAttribute('data-url');
                            console.log('Checking button with data-url:', buttonUrl);

                            if (buttonUrl && buttonUrl.includes(videoFileName)) {
                              targetButton = button;
                              console.log('✅ Found matching Watch Video button!');
                              break;
                            }
                          }

                          if (targetButton) {
                            console.log('🎬 Clicking Watch Video button...');
                            targetButton.click();

                            // Wait for the modal to appear and then look for fullscreen button (optimized)
                            setTimeout(() => {
                              console.log('🔍 Looking for fullscreen button...');

                              function checkForFullscreenButton() {
                                const fullscreenButton = document.querySelector('.vjs-fullscreen-control.vjs-control.vjs-button');

                                if (fullscreenButton) {
                                  console.log('✅ Found fullscreen button!');
                                  console.log('🎬 Clicking fullscreen button...');

                                  fullscreenButton.click();

                                  if (window.ReactNativeWebView) {
                                    window.ReactNativeWebView.postMessage(JSON.stringify({
                                      type: 'fullscreen_clicked',
                                      message: 'Fullscreen button clicked successfully',
                                      videoFileName: videoFileName
                                    }));
                                  }

                                  return true;
                                } else {
                                  console.log('⏳ Fullscreen button not found yet, waiting...');
                                  return false;
                                }
                              }

                              // Try to find the fullscreen button immediately
                              if (!checkForFullscreenButton()) {
                                // If not found, keep checking every 500ms for up to 10 seconds
                                let attempts = 0;
                                const maxAttempts = 20; // 10 seconds total

                                const fullscreenInterval = setInterval(() => {
                                  attempts++;
                                  console.log('🔍 Attempt', attempts, 'to find fullscreen button...');

                                  if (checkForFullscreenButton()) {
                                    clearInterval(fullscreenInterval);
                                  } else if (attempts >= maxAttempts) {
                                    console.log('❌ Could not find fullscreen button after', maxAttempts, 'attempts');
                                    clearInterval(fullscreenInterval);

                                    if (window.ReactNativeWebView) {
                                      window.ReactNativeWebView.postMessage(JSON.stringify({
                                        type: 'fullscreen_error',
                                        message: 'Could not find fullscreen button',
                                        videoFileName: videoFileName
                                      }));
                                    }
                                  }
                                }, 500);
                              }
                            }, 2000); // Wait 2 seconds for video player to load

                            // Send success message for watch video button click
                            if (window.ReactNativeWebView) {
                              window.ReactNativeWebView.postMessage(JSON.stringify({
                                type: 'watch_video_clicked',
                                message: 'Watch Video button clicked successfully',
                                videoFileName: videoFileName
                              }));
                            }
                          } else {
                            console.log('❌ Could not find matching Watch Video button');
                            if (window.ReactNativeWebView) {
                              window.ReactNativeWebView.postMessage(JSON.stringify({
                                type: 'watch_video_error',
                                message: 'Could not find matching Watch Video button',
                                videoFileName: videoFileName
                              }));
                            }
                          }

                        } catch (error) {
                          console.log('❌ Error in fallback video extraction:', error);
                          if (window.ReactNativeWebView) {
                            window.ReactNativeWebView.postMessage(JSON.stringify({
                              type: 'watch_video_error',
                              message: 'Error in fallback video extraction: ' + error.message
                            }));
                          }
                        }
                      })();
                    `;

                    modalWebViewRef.current.injectJavaScript(fallbackJS);
                  } else if (selectedContent?.type === 'vod' && !selectedContent?.isFallback && modalWebViewRef.current) {
                    // For regular video content, check if the page contains 404 error
                    console.log('🔍 Checking for 404 error in video content...');

                    const check404JS = `
                      (function() {
                        try {
                          // Check if the page contains 404 error text
                          const pageText = document.body.innerText || document.body.textContent || '';
                          const pageHTML = document.documentElement.innerHTML || '';

                          const is404Page = pageText.includes('404 - File or directory not found.') ||
                                          pageText.includes('404') && pageText.includes('File or directory not found') ||
                                          pageHTML.includes('404 - File or directory not found.');

                          console.log('Page text check:', pageText.substring(0, 200));
                          console.log('Is 404 page:', is404Page);

                          if (is404Page) {
                            // Send message that this is a 404 page
                            if (window.ReactNativeWebView) {
                              window.ReactNativeWebView.postMessage(JSON.stringify({
                                type: 'video_404_detected',
                                message: '404 error detected in video content'
                              }));
                            }
                          }
                        } catch (error) {
                          console.log('Error checking for 404:', error);
                        }
                      })();
                    `;

                    modalWebViewRef.current.injectJavaScript(check404JS);
                  }
                }}
                onMessage={async (event) => {
                  try {
                    const data = JSON.parse(event.nativeEvent.data);
                    console.log('Modal WebView message:', data.type);

                    switch (data.type) {
                      case 'video_404_detected':
                        console.log('🚨 404 error detected in video content, attempting fallback...');

                        const fallbackSuccess = await handleVideoFallback(
                          selectedContent.url,
                          selectedContent.text
                        );

                        if (!fallbackSuccess) {
                          Alert.alert(
                            'Video Unavailable',
                            'This video content could not be loaded. Please try again later.',
                            [{ text: 'OK', style: 'default' }]
                          );
                        }
                        break;

                      case 'watch_video_clicked':
                        console.log('✅ Watch Video button clicked successfully:', data.message);
                        console.log('🎬 Video filename:', data.videoFileName);
                        // The Watch Video button has been clicked and system will look for fullscreen button
                        break;

                      case 'fullscreen_clicked':
                        console.log('✅ Fullscreen button clicked successfully:', data.message);
                        console.log('🎬 Video should now be in fullscreen for:', data.videoFileName);
                        // Video fallback process is now complete!
                        break;

                      case 'fullscreen_error':
                        console.log('❌ Could not find fullscreen button:', data.message);
                        // This is not critical - the video should still be playable without fullscreen
                        console.log('ℹ️ Video is playable but fullscreen button was not found');
                        break;

                      case 'watch_video_error':
                        console.log('❌ Error clicking Watch Video button:', data.message);
                        Alert.alert(
                          'Video Unavailable',
                          'Could not access the video content. The video may not be available on this course page.',
                          [{ text: 'OK', style: 'default' }]
                        );
                        break;

                      case 'fallback_ready':
                        console.log('✅ Fallback WebView ready:', data.message);
                        // This case is no longer used since we directly click the button
                        break;

                      default:
                        console.log('Unknown modal message type:', data.type);
                        break;
                    }
                  } catch (error) {
                    console.log('❌ Error parsing modal WebView message:', error);
                  }
                }}
              />
            )}
          </View>
        </SafeAreaView>
      </Modal>


    </ThemeTransitionWrapper>
  );
};

// Create styles function that uses theme
const createStyles = (theme, safeCurrentThemeName) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  backButtonContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 2000,
  },
  backButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  courseTitleContainer: {
    position: 'absolute',
    top: 55,
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 1500,
  },
  courseTitle: {
    fontSize: 30,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  refreshButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 2000,
  },
  refreshButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : 'transparent',
  },
  refreshButtonLoading: {
    // Keep the same background, only the icon color changes
  },
  mainContent: {
    flex: 1,
    paddingTop: 100, // Increased margin below top bar
    paddingHorizontal: 20,
  },
  scrollContainer: {
    flex: 1,
  },
  scrollContentContainer: {
    flexGrow: 1,
    minHeight: '100%',
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
  descriptionContainer: {
    marginBottom: 25,
    backgroundColor: theme.colors.surface,
    borderRadius: 15,
    padding: 20,
    borderWidth: 3,
    borderColor: '#FFD700', // Always yellow border
  },
  descriptionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 15,
  },
  descriptionContent: {
    backgroundColor: theme.colors.background,
    borderRadius: 10,
    padding: 15,
  },
  descriptionText: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
    marginBottom: 8,
  },
  descriptionLinkContainer: {
    backgroundColor: theme.colors.primary + '20',
    borderRadius: 8,
    padding: 12,
    marginVertical: 4,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary,
  },
  descriptionLinkText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
    textDecorationLine: 'underline',
  },
  emptyAnnouncementText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: 20,
  },
  filtersContainer: {
    marginBottom: 20,
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 12, // Reduced from 16
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  filtersTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 8, // Reduced from 12
  },
  filtersButtonsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 4, // Reduced from 8
  },
  filterButton: {
    backgroundColor: theme.colors.background,
    borderRadius: 16, // Reduced from 20 for more compact look
    paddingHorizontal: 12, // Reduced from 16
    paddingVertical: 6, // Reduced from 8
    borderWidth: 1,
    borderColor: theme.colors.border,
    marginRight: 4, // Reduced from 8
    marginBottom: 4, // Reduced from 8
  },
  filterButtonSelected: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primary,
  },
  filterButtonText: {
    fontSize: 11, // Reduced from 12
    color: theme.colors.textSecondary,
    fontWeight: '500',
    textTransform: 'capitalize',
  },
  filterButtonTextSelected: {
    color: theme.colors.primaryText,
    fontWeight: 'bold',
  },
  weeklyContainer: {
    marginBottom: 20,
  },
  weekContainer: {
    marginBottom: 20,
    backgroundColor: theme.colors.surface,
    borderRadius: 15,
    padding: 15,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
  },
  weekTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 12,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  weekContent: {
    paddingLeft: 10,
  },
  contentItem: {
    marginBottom: 8,
  },
  linkContainer: {
    backgroundColor: theme.colors.primary + '20',
    borderRadius: 8,
    padding: 12,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.primary,
  },
  linkContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  linkIconContainer: {
    marginRight: 10,
  },
  linkText: {
    fontSize: 14,
    color: theme.colors.primary,
    fontWeight: '500',
    flex: 1,
  },
  contentText: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 18,
    paddingVertical: 4,
  },
  noContentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  noContentText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  webViewContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: theme.colors.background,
  },
  webView: {
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalCloseButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalCloseText: {
    fontSize: 18,
    color: theme.colors.text,
    fontWeight: 'bold',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 20,
  },
  modalShareButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  modalContent: {
    flex: 1,
  },
  modalWebView: {
    flex: 1,
  },
  // New styles for announcements and demo content
  announcementItem: {
    backgroundColor: theme.colors.background,
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
    borderLeftWidth: 3,
    borderLeftColor: theme.colors.border,
  },
  announcementItemImportant: {
    borderLeftColor: '#FFD700',
    backgroundColor: theme.colors.surface,
  },
  announcementText: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 18,
  },

});

export default ContentScreen;
