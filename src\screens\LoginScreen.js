import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  Image,
  Keyboard,
  Animated,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import { clearWebViewSession, disposeWebView } from '../utils/WebViewUtils';
import { useTheme } from '../contexts/ThemeContext';
import { useOffline } from '../contexts/OfflineContext';
import { errorLog, infoLog, warnLog } from '../utils/DebugUtils';
import Svg, { Path, Circle, Line } from 'react-native-svg';
import { isDemoUser, DEMO_STUDENT_DATA } from '../utils/DemoData';
import { initializeAccountMode } from '../utils/DemoModeHelper';
import { shouldShowOfflineChoice, getOfflineChoiceReason } from '../utils/OfflineUtils';

// Force Metro to recognize file changes by adding timestamp comment
// Updated: 2025-09-05T01:15:00Z

const LoginScreen = React.memo(({ navigation }) => {
  
  // Theme context with defensive programming
  let theme, currentThemeName;
  try {
    const themeContext = useTheme();
    theme = themeContext.theme;
    currentThemeName = themeContext.currentThemeName;
  } catch (error) {
    console.log('Theme context error:', error);
    // Fallback theme - Updated to match new dark theme
    theme = {
      colors: {
        background: '#2C2C2C', // Updated to new dark grey
        surface: '#3A3A3A', // Updated to new surface grey
        text: '#FFFFFF',
        textSecondary: '#B0B0B0', // Lightened
        primary: '#EAB308',
        primaryText: '#2C2C2C', // Updated to match new background
        error: '#EF4444',
        border: '#555555', // Lightened
        shadow: '#1A1A1A' // Lightened
      }
    };
    currentThemeName = 'dark';
  }

  // Fallback for currentThemeName to prevent undefined errors
  const safeCurrentThemeName = currentThemeName || 'dark';

  // Offline context
  const { checkNetworkConnectivity, checkOfflineMode } = useOffline();

  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [shouldLoadWebView, setShouldLoadWebView] = useState(false);
  const [submittedCredentials, setSubmittedCredentials] = useState({ username: '', password: '' });
  const [loginAttemptId, setLoginAttemptId] = useState(0);
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isAutoLogin, setIsAutoLogin] = useState(false);
  const [isCheckingStorage, setIsCheckingStorage] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const webViewRef = useRef(null);

  // Offline mode tracking
  const [hasConnectionError, setHasConnectionError] = useState(false);
  const [hasWebViewError, setHasWebViewError] = useState(false);
  const [hasTimeout, setHasTimeout] = useState(false);
  const loginTimeoutRef = useRef(null);

  // Refs for tracking background operations and component mount state
  const isMountedRef = useRef(true);
  const activeTimeoutsRef = useRef(new Set());
  const keyboardListenersRef = useRef([]);

  // Memoize styles to prevent recreation on every render (major iOS performance fix)
  const styles = useMemo(() => createStyles(theme, safeCurrentThemeName), [theme, safeCurrentThemeName]);

  // Debounced text inputs for iOS performance (prevents rapid re-renders)
  const [usernameBuffer, setUsernameBuffer] = useState('');
  const [passwordBuffer, setPasswordBuffer] = useState('');
  const usernameTimeoutRef = useRef(null);
  const passwordTimeoutRef = useRef(null);

  // Animated values for logo transitions
  const logoScale = useRef(new Animated.Value(1)).current;
  const logoTranslateY = useRef(new Animated.Value(0)).current;

  // Simplified text input handlers - maintain iOS performance while ensuring reliable credentials
  const handleUsernameChange = useCallback((text) => {
    if (!isLoading) {
      // Update buffer immediately for both platforms (for UI responsiveness)
      setUsernameBuffer(text);
      
      if (Platform.OS === 'ios') {
        // iOS: Debounce state updates to prevent flickering, but ensure sync before submit
        if (usernameTimeoutRef.current) {
          clearTimeout(usernameTimeoutRef.current);
        }
        usernameTimeoutRef.current = setTimeout(() => {
          if (isMountedRef.current) {
            setUsername(text);
          }
        }, 150); // Shorter delay for better responsiveness
      } else {
        // Android: Direct update for optimal performance
        setUsername(text);
      }
    }
  }, [isLoading]);

  const handlePasswordChange = useCallback((text) => {
    if (!isLoading) {
      // Update buffer immediately for both platforms (for UI responsiveness)
      setPasswordBuffer(text);
      
      if (Platform.OS === 'ios') {
        // iOS: Debounce state updates to prevent flickering, but ensure sync before submit
        if (passwordTimeoutRef.current) {
          clearTimeout(passwordTimeoutRef.current);
        }
        passwordTimeoutRef.current = setTimeout(() => {
          if (isMountedRef.current) {
            setPassword(text);
          }
        }, 150); // Shorter delay for better responsiveness
      } else {
        // Android: Direct update for optimal performance
        setPassword(text);
      }
    }
  }, [isLoading]);

  const handleTogglePassword = useCallback(() => {
    setShowPassword(!showPassword);
  }, [showPassword]);

  // Comprehensive function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 LoginScreen: Killing all background operations...');

    // Mark component as unmounted to prevent state updates
    isMountedRef.current = false;

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();

    // Stop all animations
    logoScale.stopAnimation();
    logoTranslateY.stopAnimation();

    // Remove keyboard listeners
    keyboardListenersRef.current.forEach(listener => {
      listener?.remove();
    });
    keyboardListenersRef.current = [];

    // Reset all loading states to prevent issues
    setIsLoading(false);
    setIsCheckingStorage(false);

    // Dispose of WebView
    await disposeWebView(webViewRef, 'login-webview');

    // Clear debounce timeouts
    if (usernameTimeoutRef.current) {
      clearTimeout(usernameTimeoutRef.current);
      usernameTimeoutRef.current = null;
    }
    if (passwordTimeoutRef.current) {
      clearTimeout(passwordTimeoutRef.current);
      passwordTimeoutRef.current = null;
    }
    if (loginTimeoutRef.current) {
      clearTimeout(loginTimeoutRef.current);
      loginTimeoutRef.current = null;
    }

    console.log('✅ LoginScreen: All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ LoginScreen: Prevented ${stateName} state update after unmount`);
    }
  };

  // Safe timeout wrapper that tracks timeouts for cleanup
  const safeSetTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      activeTimeoutsRef.current.delete(timeoutId);
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  // Function to decode HTML entities
  const decodeHtmlEntities = (text) => {
    if (!text) return text;

    return text
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
      .trim();
  };

  // Extract student information from HTML and store in localStorage
  const extractStudentInfo = async (html, retryCount = 0, maxRetries = 3) => {
    try {
      console.log(`🔍 LoginScreen: Extracting student info from HTML (attempt ${retryCount + 1}/${maxRetries})...`);

      if (!html || html.length < 100) {
        throw new Error('HTML content is too short or empty');
      }

      const extracted = {};

      // Extract Full Name from ContentPlaceHolderright_ContentPlaceHoldercontent_LabelFullName
      const fullNameMatch = html.match(/<[^>]*id="ContentPlaceHolderright_ContentPlaceHoldercontent_LabelFullName"[^>]*>([^<]+)<\/[^>]*>/i);
      if (fullNameMatch && fullNameMatch[1]) {
        const fullName = decodeHtmlEntities(fullNameMatch[1].trim());
        if (fullName && fullName !== '' && fullName !== '❌') {
          extracted.fullName = fullName;
        }
      }

      // Extract Student ID from ContentPlaceHolderright_ContentPlaceHoldercontent_LabelUniqAppNo
      const studentIdMatch = html.match(/<[^>]*id="ContentPlaceHolderright_ContentPlaceHoldercontent_LabelUniqAppNo"[^>]*>([^<]+)<\/[^>]*>/i);
      if (studentIdMatch && studentIdMatch[1]) {
        const studentId = decodeHtmlEntities(studentIdMatch[1].trim());
        if (studentId && studentId !== '' && studentId !== '❌') {
          extracted.studentId = studentId;
        }
      }

      // Extract Faculty from ContentPlaceHolderright_ContentPlaceHoldercontent_Labelfaculty
      const facultyMatch = html.match(/<[^>]*id="ContentPlaceHolderright_ContentPlaceHoldercontent_Labelfaculty"[^>]*>([^<]+)<\/[^>]*>/i);
      if (facultyMatch && facultyMatch[1]) {
        const faculty = decodeHtmlEntities(facultyMatch[1].trim());
        if (faculty && faculty !== '' && faculty !== '❌') {
          extracted.faculty = faculty;
        }
      }

      console.log('📋 LoginScreen: Extracted student info:', {
        fullName: extracted.fullName || '❌',
        studentId: extracted.studentId || '❌',
        faculty: extracted.faculty || '❌'
      });

      // Check if extraction was successful
      const hasAllRequiredData = extracted.fullName && extracted.studentId && extracted.faculty;

      if (!hasAllRequiredData && retryCount < maxRetries - 1) {
        console.log(`⚠️ Incomplete extraction (attempt ${retryCount + 1}), retrying...`);
        await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second before retry
        return await extractStudentInfo(html, retryCount + 1, maxRetries);
      }

      if (!hasAllRequiredData) {
        console.log('❌ Failed to extract all required student data after all retries');
        console.log('🔍 HTML snippet for debugging:', html.substring(0, 500));
        throw new Error('Failed to extract required student information');
      }

      // Store in localStorage only if extraction was successful
      try {
        await AsyncStorage.setItem('student_fullName', extracted.fullName);
        await AsyncStorage.setItem('student_id', extracted.studentId);
        await AsyncStorage.setItem('student_faculty', extracted.faculty);
        await AsyncStorage.setItem('student_data_timestamp', Date.now().toString());

        console.log('✅ LoginScreen: All student info cached successfully:', {
          fullName: extracted.fullName,
          studentId: extracted.studentId,
          faculty: extracted.faculty
        });
      } catch (storageError) {
        console.log('❌ LoginScreen: Error caching student info:', storageError);
      }

      return extracted;
    } catch (error) {
      console.log(`❌ LoginScreen: Error extracting student info (attempt ${retryCount + 1}):`, error);

      if (retryCount < maxRetries - 1) {
        console.log(`🔄 Retrying due to error (${retryCount + 1}/${maxRetries})...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return await extractStudentInfo(html, retryCount + 1, maxRetries);
      }

      console.log('❌ All retry attempts failed for student info extraction');
      return {
        fullName: '',
        studentId: '',
        faculty: ''
      };
    }
  };

  // Load credentials from local storage
  const loadStoredCredentials = async () => {
    try {
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (storedUsername && storedPassword) {
        console.log('Found stored credentials, checking if demo user...');

        // Check if this is a demo user first
        if (isDemoUser(storedUsername, storedPassword)) {
          console.log('🎭 Demo user detected in auto-login - proceeding with demo mode');
          
          // Set both state and buffer values for consistency
          setUsername(storedUsername);
          setPassword(storedPassword);
          setUsernameBuffer(storedUsername);
          setPasswordBuffer(storedPassword);
          
          // Mark as checking storage complete
          safeSetState(setIsCheckingStorage, false, 'isCheckingStorage');
          
          // Auto-login demo user directly without WebView
          console.log('🎭 Auto-logging in demo user...');
          setIsAutoLogin(true);
          setIsLoading(true);
          
          // Initialize demo account mode
          await initializeAccountMode(true, false); // Don't clear caches for auto-login
          
          // Store demo student data
          await AsyncStorage.setItem('student_fullName', DEMO_STUDENT_DATA.fullName);
          await AsyncStorage.setItem('student_id', DEMO_STUDENT_DATA.studentId);
          await AsyncStorage.setItem('student_faculty', DEMO_STUDENT_DATA.faculty);
          
          console.log('🎭 Demo user auto-login complete - navigating to Portal');
          
          // Navigate directly to Portal with demo data
          const webViewData = {
            html: '<html><body>Demo Mode Auto-Login</body></html>',
            url: 'demo://auto-login',
            title: 'Demo Mode Auto-Login',
            credentials: { username: storedUsername, password: storedPassword },
            studentData: DEMO_STUDENT_DATA,
            isAutoLogin: true,
            isDemoMode: true
          };
          
          setIsLoading(false);
          navigation.navigate('Portal', { webViewData });
          return;
        }

        console.log('Regular user detected, attempting auto-login...');

        // Check network connectivity first for regular users
        const hasConnection = await checkNetworkConnectivity();
        console.log('🌐 Network connectivity check result:', hasConnection);

        if (!hasConnection) {
          console.log('❌ No network connection - showing offline choice');
          setHasConnectionError(true);
          navigation.navigate('OfflineChoice', { reason: 'no_connection' });
          return;
        }

        // Set both state and buffer values for consistency
        setUsername(storedUsername);
        setPassword(storedPassword);
        setUsernameBuffer(storedUsername);
        setPasswordBuffer(storedPassword);

        console.log('🔍 Auto-login credentials loaded:', {
          username: storedUsername ? `${storedUsername.substring(0, 3)}***` : 'EMPTY',
          password: storedPassword ? `***${storedPassword.length} chars` : 'EMPTY'
        });

        // Clear WebView session before auto-login
        await clearWebViewSession();

        // Set up timeout for auto-login (10 seconds)
        loginTimeoutRef.current = setTimeout(() => {
          console.log('⏰ Auto-login timeout reached');
          setHasTimeout(true);
          navigation.navigate('OfflineChoice', { reason: 'server_timeout' });
        }, 10000);

        // Attempt auto-login
        setIsAutoLogin(true);
        setSubmittedCredentials({ username: storedUsername, password: storedPassword });
        setLoginAttemptId(1);
        setShouldLoadWebView(true);
      } else {
        console.log('No stored credentials found');
        safeSetState(setIsCheckingStorage, false, 'isCheckingStorage');
      }
    } catch (error) {
      console.log('Error loading stored credentials:', error);
      safeSetState(setIsCheckingStorage, false, 'isCheckingStorage');
    }
  };

  const handleSubmit = async () => {
    // Force sync any pending debounced updates immediately
    if (Platform.OS === 'ios') {
      if (usernameTimeoutRef.current) {
        clearTimeout(usernameTimeoutRef.current);
        usernameTimeoutRef.current = null;
      }
      if (passwordTimeoutRef.current) {
        clearTimeout(passwordTimeoutRef.current);
        passwordTimeoutRef.current = null;
      }
      // Force immediate sync of buffer values to state
      if (usernameBuffer && usernameBuffer !== username) {
        setUsername(usernameBuffer);
      }
      if (passwordBuffer && passwordBuffer !== password) {
        setPassword(passwordBuffer);
      }
    }

    // Use buffer values as primary source (they're always most up-to-date)
    const finalUsername = usernameBuffer || username;
    const finalPassword = passwordBuffer || password;
    
    console.log('🔍 Final credentials before submit:', {
      platform: Platform.OS,
      finalUsername: finalUsername ? `${finalUsername.substring(0, 3)}***` : 'EMPTY',
      finalPassword: finalPassword ? `***${finalPassword.length} chars` : 'EMPTY',
      bufferUsername: usernameBuffer ? `${usernameBuffer.substring(0, 3)}***` : 'EMPTY',
      bufferPassword: passwordBuffer ? `***${passwordBuffer.length} chars` : 'EMPTY',
      stateUsername: username ? `${username.substring(0, 3)}***` : 'EMPTY',
      statePassword: password ? `***${password.length} chars` : 'EMPTY'
    });
    
    if (!finalUsername || !finalPassword) {
      setErrorMessage('Please enter both username and password');
      console.log('❌ Empty credentials detected - cannot submit');
      return;
    }

    // Check if this is a demo user
    if (isDemoUser(finalUsername, finalPassword)) {
      console.log('🎭 Demo user detected - bypassing WebView authentication');

      // Clear any previous error messages and start loading
      setErrorMessage('');
      setIsAutoLogin(false);
      setIsLoading(true);

      // Dismiss keyboard and immediately reset logo position (iOS optimized)
      Keyboard.dismiss();

      // Force logo back to normal position and size immediately - iOS specific handling
      if (Platform.OS === 'ios') {
        // Immediate reset without animation to prevent flickering during loading
        logoScale.setValue(1);
        logoTranslateY.setValue(0);
      } else {
        // Keep animation for Android
        Animated.parallel([
          Animated.timing(logoScale, {
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(logoTranslateY, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      }

      // Initialize demo account mode - clear caches for fresh demo login
      console.log('🧹 Initializing demo mode...');
      await initializeAccountMode(true, true); // Always clear caches for demo login

      // Store demo credentials and student data properly
      await AsyncStorage.setItem('guc_username', finalUsername);
      await AsyncStorage.setItem('guc_password', finalPassword);
      await AsyncStorage.setItem('student_fullName', DEMO_STUDENT_DATA.fullName);
      await AsyncStorage.setItem('student_id', DEMO_STUDENT_DATA.studentId);
      await AsyncStorage.setItem('student_faculty', DEMO_STUDENT_DATA.faculty);

      console.log('🎭 Demo credentials and student data stored');

      // Simulate brief loading delay for better UX
      setTimeout(() => {
        setIsLoading(false);

        // Navigate to Portal with demo data
        const webViewData = {
          html: '<html><body>Demo Mode</body></html>',
          url: 'demo://mode',
          title: 'Demo Mode',
          credentials: { username: finalUsername, password: finalPassword },
          studentData: DEMO_STUDENT_DATA,
          isAutoLogin: false,
          isDemoMode: true
        };

        console.log('🎭 Navigating to Portal in demo mode');
        navigation.navigate('Portal', { webViewData });
      }, 1500); // Brief delay to show loading state

      return; // Exit early for demo user
    }

    const newAttemptId = loginAttemptId + 1;
    console.log('✅ Attempting manual login to GUC website with valid credentials...');

    // Clear any previous error messages and start loading
    setErrorMessage('');
    setIsAutoLogin(false);
    setIsLoading(true);

    // Dismiss keyboard and immediately reset logo position (iOS optimized)
    Keyboard.dismiss();

    // Force logo back to normal position and size immediately - iOS specific handling
    if (Platform.OS === 'ios') {
      // Immediate reset without animation to prevent flickering during loading
      logoScale.setValue(1);
      logoTranslateY.setValue(0);
    } else {
      // Keep animation for Android
      Animated.parallel([
        Animated.timing(logoScale, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(logoTranslateY, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }

    // Clear WebView session before attempting login
    await clearWebViewSession();

    // Use the final, verified credentials
    setSubmittedCredentials({ username: finalUsername, password: finalPassword });
    setLoginAttemptId(newAttemptId);
    setShouldLoadWebView(true);
  };

  // Check for stored credentials on component mount
  useEffect(() => {
    loadStoredCredentials();

    // Cleanup function
    return () => {
      console.log('🧹 LoginScreen: Component unmounting - cleaning up...');
      killAllBackgroundOperations();
    };
  }, []);

  // Handle navigation focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 LoginScreen: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 LoginScreen: Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Kill background operations when navigating away completely
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 LoginScreen: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  // Keyboard visibility listeners with iOS-optimized animations
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      if (!isMountedRef.current || isLoading) return; // Skip animations during loading
      
      safeSetState(setIsKeyboardVisible, true, 'isKeyboardVisible');
      
      // Only animate logo on iOS and when not loading to prevent flickering
      if (Platform.OS === 'ios' && !isLoading) {
        // Use more conservative animations for iOS
        Animated.parallel([
          Animated.timing(logoScale, {
            toValue: 0.8, // Less aggressive scaling (80% instead of 70%)
            duration: 200, // Faster animation
            useNativeDriver: true,
          }),
          Animated.timing(logoTranslateY, {
            toValue: -20, // Less movement (20px instead of 30px)
            duration: 200, // Faster animation
            useNativeDriver: true,
          }),
        ]).start();
      }
    });

    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      if (!isMountedRef.current) return;
      
      safeSetState(setIsKeyboardVisible, false, 'isKeyboardVisible');
      
      // Only animate back if we're not in loading state to prevent conflicts
      if (Platform.OS === 'ios' && !isLoading) {
        Animated.parallel([
          Animated.timing(logoScale, { // Use timing instead of spring for consistency
            toValue: 1,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(logoTranslateY, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }),
        ]).start();
      }
    });

    // Track listeners for cleanup
    keyboardListenersRef.current = [keyboardDidShowListener, keyboardDidHideListener];

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
      keyboardListenersRef.current = [];
    };
  }, [logoScale, logoTranslateY, isLoading]); // Add isLoading dependency

  // Network connectivity listener for connection loss during app usage
  useEffect(() => {
    const checkConnectionPeriodically = async () => {
      if (!isAutoLogin && !isLoading) return; // Only check during auto-login or loading

      const hasConnection = await checkNetworkConnectivity();
      if (!hasConnection && (isAutoLogin || isLoading)) {
        console.log('❌ Connection lost during login process');
        setHasConnectionError(true);
        if (loginTimeoutRef.current) {
          clearTimeout(loginTimeoutRef.current);
        }
        navigation.navigate('OfflineChoice', { reason: 'connection_lost' });
      }
    };

    // Only set up interval if not already navigated away and not demo user
    let connectionCheckInterval = null;
    
    // Check if we should start the interval (async check) - avoid creating interval for demo users
    const initializeInterval = async () => {
      // First check if we have demo credentials to avoid creating interval at all
      const currentUsername = username || usernameBuffer;
      const currentPassword = password || passwordBuffer;
      
      // If we have credentials, check if they are demo credentials
      if (currentUsername && currentPassword) {
        const isDemo = await isDemoUser(currentUsername, currentPassword);
        if (isDemo) {
          console.log('🎭 Demo user detected - not creating periodic network check interval');
          return; // Exit early, don't create interval
        }
      }
      
      // Only create interval for non-demo users
      if (isMountedRef.current) {
        console.log('🟢 Starting periodic network check for real user');
        connectionCheckInterval = setInterval(checkConnectionPeriodically, 5000);
      }
    };
    
    // Only initialize the interval if we have credentials (avoid running for empty state)
    const currentUsername = username || usernameBuffer;
    const currentPassword = password || passwordBuffer;
    
    if (currentUsername && currentPassword) {
      initializeInterval();
    } else {
      console.log('⏸️ No credentials available - skipping interval initialization');
    }

    return () => {
      if (connectionCheckInterval) {
        console.log('🔴 Clearing periodic network check interval');
        clearInterval(connectionCheckInterval);
      }
    };
  }, [isAutoLogin, isLoading, navigation, checkNetworkConnectivity, username, usernameBuffer, password, passwordBuffer]);

  // Handle navigation cleanup when leaving LoginScreen
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧩 LoginScreen: Component unmounting - cleaning up all operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  const saveCredentials = async (username, password) => {
    try {
      await AsyncStorage.setItem('guc_username', username);
      await AsyncStorage.setItem('guc_password', password);
    } catch (error) {
    }
  };

  const onWebViewMessage = async (event) => {
    try {
      // Add null checks for event data
      if (!event || !event.nativeEvent || !event.nativeEvent.data) {
        console.log('❌ Invalid event data received');
        return;
      }

      const data = JSON.parse(event.nativeEvent.data);

      if (data.type === 'page_html') {
        // Ensure data.html exists before checking content
        const htmlContent = data.html || '';

        // Check for 401 unauthorized error
        if (htmlContent.includes('401 - Unauthorized: Access is denied due to invalid credentials')) {
          console.log('❌ Login failed - Invalid credentials detected');

          if (isAutoLogin) {
            console.log('Auto-login failed, clearing stored credentials');
            // Clear stored credentials if auto-login failed
            try {
              await AsyncStorage.removeItem('guc_username');
              await AsyncStorage.removeItem('guc_password');
            } catch (error) {
              console.log('Error clearing credentials:', error);
            }
            setErrorMessage('Stored credentials are invalid. Please login again.');
          } else {
            setErrorMessage('Incorrect username or password. Please try again.');
          }

          safeSetState(setIsAutoLogin, false, 'isAutoLogin');
          safeSetState(setIsCheckingStorage, false, 'isCheckingStorage');
          safeSetState(setShouldLoadWebView, false, 'shouldLoadWebView');
          safeSetState(setIsLoading, false, 'isLoading');
          return;
        }

        // Check for successful login (redirected to index.aspx)
        const currentUrl = data.url || '';
        if (currentUrl.includes('index.aspx')) {
          console.log('✅ Login successful');

          // Clear login timeout since login was successful
          if (loginTimeoutRef.current) {
            clearTimeout(loginTimeoutRef.current);
            loginTimeoutRef.current = null;
          }

          let studentData = { fullName: '', studentId: '', faculty: '' };

          if (isAutoLogin) {
            console.log('✅ Auto-login successful - Preserving cached data');
            // For auto-login, preserve caches and use existing data
            await initializeAccountMode(false, false); // Don't clear caches for auto-login

            // Load existing cached data for navigation
            try {
              const cachedFullName = await AsyncStorage.getItem('student_fullName');
              const cachedStudentId = await AsyncStorage.getItem('student_id');
              const cachedFaculty = await AsyncStorage.getItem('student_faculty');

              studentData = {
                fullName: cachedFullName || '',
                studentId: cachedStudentId || '',
                faculty: cachedFaculty || ''
              };

              console.log('📋 Using cached student data:', {
                fullName: studentData.fullName ? '✅' : '❌',
                studentId: studentData.studentId ? '✅' : '❌',
                faculty: studentData.faculty ? '✅' : '❌'
              });

              // If no cached data found during auto-login, that's okay - Portal will handle it
              if (!studentData.fullName && !studentData.studentId && !studentData.faculty) {
                console.log('⚠️ No cached student data found during auto-login - Portal will load from cache or show placeholder');
              }
            } catch (error) {
              console.log('⚠️ Error loading cached student data:', error);
            }
          } else {
            console.log('✅ Manual login successful - Clearing caches and extracting fresh data');
            // For manual login, clear caches to ensure fresh data
            await initializeAccountMode(false, true); // Clear caches for manual login

            saveCredentials(submittedCredentials.username, submittedCredentials.password);

            // Only extract student data for manual login (fresh login)
            console.log('🔍 LoginScreen: Extracting student data before navigation...');
            studentData = await extractStudentInfo(data.html || '');

            // If extraction failed, try to reload the page and extract again
            if (!studentData.fullName || !studentData.studentId || !studentData.faculty) {
              console.log('⚠️ Initial extraction failed, attempting page reload and retry...');

              try {
                // Reload the WebView to get fresh content
                if (webViewRef.current) {
                  webViewRef.current.reload();

                  // Wait for page to reload and try extraction again
                  await new Promise(resolve => setTimeout(resolve, 3000));

                  // Inject JavaScript to get fresh HTML
                  webViewRef.current.injectJavaScript(`
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                      type: 'retry_extraction',
                      html: document.documentElement.outerHTML,
                      url: window.location.href,
                      title: document.title
                    }));
                  `);

                  console.log('🔄 Page reload initiated for retry extraction');
                }
              } catch (reloadError) {
                console.log('❌ Error during page reload:', reloadError);
              }
            }
          }

          // Navigate to Portal with WebView data
          safeSetState(setIsLoading, false, 'isLoading');

          // Ensure data is valid before navigation
          const webViewData = {
            html: data.html || '',
            url: data.url || '',
            title: data.title || '',
            credentials: submittedCredentials || { username: '', password: '' },
            studentData: studentData, // Include student data (fresh or cached)
            isAutoLogin: isAutoLogin // Include login type for debugging
          };

          // Validate student data before navigation
          const hasValidStudentData = studentData.fullName &&
                                     studentData.studentId &&
                                     studentData.faculty &&
                                     studentData.fullName !== '❌' &&
                                     studentData.studentId !== '❌' &&
                                     studentData.faculty !== '❌';

          console.log('✅ Student data validation:', {
            fullName: studentData.fullName,
            studentId: studentData.studentId,
            faculty: studentData.faculty,
            hasValidData: hasValidStudentData
          });

          if (!hasValidStudentData) {
            console.log('❌ Cannot navigate to Portal: Missing required student data');
            console.log('📋 Required fields: fullName, studentId, faculty, enrollmentYear');

            // Show error message to user
            setErrorMessage('Failed to extract student information. Please try logging in again.');
            setIsLoading(false);
            return;
          }

          // Calculate enrollment year from student ID
          const getEnrollmentYear = (studentId) => {
            if (!studentId) return null;
            const match = studentId.match(/^(\d+)-/);
            if (!match) return null;
            const digits = match[1];
            const firstTwoDigits = parseInt(digits.substring(0, 2));

            if (firstTwoDigits >= 64 && firstTwoDigits <= 66) {
              return '2024-2025';
            } else if (firstTwoDigits >= 61 && firstTwoDigits <= 63) {
              return '2023-2024';
            } else if (firstTwoDigits >= 58 && firstTwoDigits <= 60) {
              return '2022-2023';
            } else {
              return null;
            }
          };

          const enrollmentYear = getEnrollmentYear(studentData.studentId);
          if (!enrollmentYear) {
            console.log('❌ Cannot determine enrollment year from student ID:', studentData.studentId);
            setErrorMessage('Failed to determine enrollment year. Please try logging in again.');
            setIsLoading(false);
            return;
          }

          console.log('✅ All required data validated, navigating to Portal:', {
            hasHtml: !!webViewData.html,
            url: webViewData.url,
            title: webViewData.title,
            hasCredentials: !!webViewData.credentials,
            fullName: studentData.fullName,
            studentId: studentData.studentId,
            faculty: studentData.faculty,
            enrollmentYear: enrollmentYear,
            isAutoLogin: isAutoLogin,
            studentDataSource: isAutoLogin ? 'cached' : 'freshly extracted'
          });

          infoLog('Login successful, navigating to Portal', {
            url: webViewData.url,
            title: webViewData.title,
            hasHtml: !!webViewData.html,
            hasValidStudentData: true,
            fullName: studentData.fullName,
            studentId: studentData.studentId,
            faculty: studentData.faculty,
            enrollmentYear: enrollmentYear,
            isAutoLogin,
            studentDataSource: isAutoLogin ? 'cached' : 'freshly extracted'
          });

          navigation.navigate('Portal', { webViewData });
        }
      } else if (data.type === 'retry_extraction') {
        console.log('🔄 Retry extraction message received');

        // Try extracting student data from the fresh HTML
        const retryStudentData = await extractStudentInfo(data.html || '');

        if (retryStudentData.fullName && retryStudentData.studentId && retryStudentData.faculty) {
          console.log('✅ Retry extraction successful!');

          // Calculate enrollment year from student ID
          const getEnrollmentYear = (studentId) => {
            if (!studentId) return null;
            const match = studentId.match(/^(\d+)-/);
            if (!match) return null;
            const digits = match[1];
            const firstTwoDigits = parseInt(digits.substring(0, 2));

            if (firstTwoDigits >= 64 && firstTwoDigits <= 66) {
              return '2024-2025';
            } else if (firstTwoDigits >= 61 && firstTwoDigits <= 63) {
              return '2023-2024';
            } else if (firstTwoDigits >= 58 && firstTwoDigits <= 60) {
              return '2022-2023';
            } else {
              return null;
            }
          };

          // Validate and navigate with the retry data
          const enrollmentYear = getEnrollmentYear(retryStudentData.studentId);
          if (enrollmentYear) {
            const webViewData = {
              html: data.html || '',
              url: data.url || '',
              title: data.title || '',
              credentials: submittedCredentials || { username: '', password: '' },
              studentData: retryStudentData,
              isAutoLogin: false
            };

            console.log('✅ Retry successful, navigating to Portal with extracted data');
            navigation.navigate('Portal', { webViewData });
          } else {
            console.log('❌ Retry failed: Cannot determine enrollment year');
            setErrorMessage('Failed to determine enrollment year. Please try logging in again.');
            setIsLoading(false);
          }
        } else {
          console.log('❌ Retry extraction also failed');
          setErrorMessage('Failed to extract student information after retry. Please try logging in again.');
          setIsLoading(false);
        }
      }
    } catch (error) {
      console.log('❌ Error processing WebView message:', error);
      errorLog('WebView message processing error', {
        error: error.message,
        stack: error.stack,
        context: 'LoginScreen.onWebViewMessage'
      });
      safeSetState(setIsLoading, false, 'isLoading');
      safeSetState(setErrorMessage, 'An error occurred during login. Please try again.', 'errorMessage');
    }
  };

  const onNavigationStateChange = (navState) => {

    // Wait 500ms then log current page and inject HTML logging (optimized from 1s)
    safeSetTimeout(() => {

      // Inject JavaScript to get the page HTML
      if (webViewRef.current && !navState.loading) {
        webViewRef.current.injectJavaScript(`

          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'page_html',
            html: document.documentElement.outerHTML,
            url: window.location.href,
            title: document.title
          }));
        `);
      }
    }, 500); // Wait 500ms (optimized from 1s)
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
        enabled={true} // Always enabled to prevent layout shifts
      >
        <View style={styles.content}>
          {/* myGUC Logo */}
          <View style={styles.logoContainer}>
            <Animated.Image
              source={require('../../assets/myGUCTransparent.png')}
              style={[
                styles.logo,
                {
                  transform: [
                    { scale: logoScale },
                    { translateY: logoTranslateY }
                  ]
                }
              ]}
              resizeMode="contain"
            />
          </View>

          {/* Show loading state during auto-login check */}
          {isCheckingStorage ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Checking stored credentials...</Text>
            </View>
          ) : isAutoLogin ? (
            <View style={styles.loadingContainer}>
              <Text style={styles.loadingText}>Logging you in automatically...</Text>
            </View>
          ) : (
            <View style={styles.formContainer}>
              {/* Username Section */}
              <Text style={styles.label}>Username</Text>
              <TextInput
                style={[styles.input, isLoading && styles.inputDisabled]}
                value={usernameBuffer} // Always use buffer for immediate UI updates
                onChangeText={handleUsernameChange}
                placeholder="Enter your username"
                placeholderTextColor="#9CA3AF"
                autoCapitalize="none"
                autoCorrect={false}
                editable={!isLoading}
                // iOS specific props to reduce flickering
                {...(Platform.OS === 'ios' && {
                  clearButtonMode: 'while-editing',
                  enablesReturnKeyAutomatically: true,
                  keyboardType: 'ascii-capable', // Prevents predictive text issues
                })}
              />

              {/* Password Section */}
              <Text style={styles.label}>Password</Text>
              <View style={styles.passwordContainer}>
                <TextInput
                  style={[styles.passwordInput, isLoading && styles.inputDisabled]}
                  value={passwordBuffer} // Always use buffer for immediate UI updates
                  onChangeText={handlePasswordChange}
                  placeholder="Enter your password"
                  placeholderTextColor="#9CA3AF"
                  secureTextEntry={!showPassword}
                  autoCapitalize="none"
                  autoCorrect={false}
                  onSubmitEditing={!isLoading ? handleSubmit : undefined}
                  returnKeyType="go"
                  editable={!isLoading}
                  // iOS specific props to reduce flickering
                  {...(Platform.OS === 'ios' && {
                    enablesReturnKeyAutomatically: true,
                    textContentType: 'password',
                    keyboardType: 'ascii-capable', // Prevents predictive text issues
                  })}
                />
                <TouchableOpacity
                  style={styles.eyeButton}
                  onPress={handleTogglePassword}
                >
                  {showPassword ? (
                    <Svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9CA3AF" strokeWidth="2">
                      <Path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"/>
                      <Line x1="1" y1="1" x2="23" y2="23"/>
                    </Svg>
                  ) : (
                    <Svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="#9CA3AF" strokeWidth="2">
                      <Path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                      <Circle cx="12" cy="12" r="3"/>
                    </Svg>
                  )}
                </TouchableOpacity>
              </View>

              {/* Error Message */}
              {errorMessage ? (
                <View style={styles.errorContainer}>
                  <Text style={styles.errorText}>{errorMessage}</Text>
                </View>
              ) : null}

              {/* Submit Button */}
              <TouchableOpacity
                style={[styles.submitButton, isLoading && styles.submitButtonDisabled]}
                onPress={handleSubmit}
                disabled={isLoading}
              >
                <Text style={styles.submitButtonText}>
                  {isLoading ? 'Signing In...' : 'Login'}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>

      </KeyboardAvoidingView>

      {/* Hidden WebView for GUC login - Platform-specific positioning */}
      {shouldLoadWebView && submittedCredentials.username && submittedCredentials.password && loginAttemptId > 0 ? (
        Platform.OS === 'ios' ? (
          <View style={{ position: 'absolute', top: -100000, left: -100000, width: 0, height: 0, overflow: 'hidden' }}>
            <WebView
              ref={webViewRef}
              source={{
                uri: `https://${encodeURIComponent(submittedCredentials.username)}:${encodeURIComponent(submittedCredentials.password)}@apps.guc.edu.eg/student_ext/`
              }}
              style={styles.hiddenWebView}
              onMessage={onWebViewMessage}
              onNavigationStateChange={onNavigationStateChange}
              onHttpError={(syntheticEvent) => {
                const { nativeEvent } = syntheticEvent;
                console.log('❌ WebView HTTP Error:', nativeEvent);
                if (isAutoLogin) {
                  setHasWebViewError(true);
                  clearTimeout(loginTimeoutRef.current);
                  navigation.navigate('OfflineChoice', { reason: 'server_error' });
                }
              }}
              onError={(syntheticEvent) => {
                const { nativeEvent } = syntheticEvent;
                console.log('❌ WebView Error:', nativeEvent);
                if (isAutoLogin) {
                  setHasWebViewError(true);
                  clearTimeout(loginTimeoutRef.current);
                  navigation.navigate('OfflineChoice', { reason: 'server_error' });
                }
              }}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={false}
              scalesPageToFit={true}
              mixedContentMode="compatibility"
              allowsInlineMediaPlayback={true}
              cacheEnabled={false}
              incognito={true}
              key={`login-attempt-${loginAttemptId}-${Date.now()}`} // Force fresh WebView with timestamp
            />
          </View>
        ) : (
          <WebView
            ref={webViewRef}
            source={{
              uri: `https://${encodeURIComponent(submittedCredentials.username)}:${encodeURIComponent(submittedCredentials.password)}@apps.guc.edu.eg/student_ext/`
            }}
            style={styles.hiddenWebView}
            onMessage={onWebViewMessage}
            onNavigationStateChange={onNavigationStateChange}
            onHttpError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.log('❌ WebView HTTP Error:', nativeEvent);
              if (isAutoLogin) {
                setHasWebViewError(true);
                clearTimeout(loginTimeoutRef.current);
                navigation.navigate('OfflineChoice', { reason: 'server_error' });
              }
            }}
            onError={(syntheticEvent) => {
              const { nativeEvent } = syntheticEvent;
              console.log('❌ WebView Error:', nativeEvent);
              if (isAutoLogin) {
                setHasWebViewError(true);
                clearTimeout(loginTimeoutRef.current);
                navigation.navigate('OfflineChoice', { reason: 'server_error' });
              }
            }}
            javaScriptEnabled={true}
            domStorageEnabled={true}
            startInLoadingState={false}
            scalesPageToFit={true}
            mixedContentMode="compatibility"
            allowsInlineMediaPlayback={true}
            cacheEnabled={false}
            incognito={true}
            key={`login-attempt-${loginAttemptId}-${Date.now()}`} // Force fresh WebView with timestamp
          />
        )
      ) : null}
    </SafeAreaView >
  );
});

// Create styles function that uses theme
const createStyles = (theme, safeCurrentThemeName) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 32,
    paddingVertical: 40,
  },
  logoContainer: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  logo: {
    width: '90%',
    height: '80%',
    maxWidth: 500,
    maxHeight: 300,
  },
  formContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'stretch',
    paddingBottom: 80,
  },
  label: {
    fontSize: 16,
    color: theme.colors.text,
    marginBottom: 8,
    marginTop: 20,
  },
  input: {
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.border,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: theme.colors.text,
    marginBottom: 16,
  },
  inputDisabled: {
    backgroundColor: '#4A4A4A', // Updated to match new surfaceSecondary
    borderColor: '#666666', // Lightened
    color: '#888888', // Lightened
    opacity: 0.6,
  },
  passwordContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  passwordInput: {
    backgroundColor: theme.colors.surface,
    borderWidth: 2,
    borderColor: theme.colors.border,
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingRight: 50, // Make room for eye icon
    fontSize: 16,
    color: theme.colors.text,
  },
  eyeButton: {
    position: 'absolute',
    right: 12,
    top: 12,
    padding: 4,
  },

  errorContainer: {
    backgroundColor: theme.colors.error,
    borderRadius: 8,
    padding: 12,
    marginTop: 16,
  },
  errorText: {
    color: theme.colors.text,
    fontSize: 14,
    textAlign: 'center',
    fontWeight: '500',
  },
  submitButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 32,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  submitButtonText: {
    color: theme.colors.primaryText,
    fontSize: 18,
    fontWeight: 'bold',
  },
  submitButtonDisabled: {
    backgroundColor: '#999999',
    opacity: 0.7,
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 40,
  },
  loadingText: {
    fontSize: 18,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  hiddenWebView: {
    position: 'absolute',
    top: Platform.OS === 'ios' ? -50000 : -10000,
    left: Platform.OS === 'ios' ? -50000 : -10000,
    width: Platform.OS === 'ios' ? 0.1 : 1,
    height: Platform.OS === 'ios' ? 0.1 : 1,
    opacity: 0,
    zIndex: -999,
    ...(Platform.OS === 'ios' && {
      transform: [{ scale: 0 }],
      pointerEvents: 'none',
    }),
  },
});

export default LoginScreen;
