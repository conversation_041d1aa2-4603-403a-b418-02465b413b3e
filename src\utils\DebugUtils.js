import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Debug utilities for production APK debugging with Hermes
 */

class DebugUtils {
  static isProduction = !__DEV__;
  static isHermesEnabled = !!global.HermesInternal;
  static debugLogs = [];
  static maxLogEntries = 1000;

  /**
   * Initialize debugging features
   */
  static init() {
    console.log('🔧 DebugUtils initialized');
    console.log(`📱 Platform: ${Platform.OS}`);
    console.log(`🚀 Production: ${this.isProduction}`);
    console.log(`⚡ Hermes: ${this.isHermesEnabled}`);
    
    if (this.isHermesEnabled) {
      this.logHermesInfo();
    }

    // Set up crash reporting
    this.setupCrashReporting();
    
    // Set up performance monitoring
    this.setupPerformanceMonitoring();
  }

  /**
   * Log Hermes engine information
   */
  static logHermesInfo() {
    try {
      if (global.HermesInternal) {
        const runtimeProps = global.HermesInternal.getRuntimeProperties?.();
        console.log('⚡ Hermes Runtime Properties:', runtimeProps);
        
        // Log memory usage if available
        if (global.HermesInternal.getInstrumentedStats) {
          const stats = global.HermesInternal.getInstrumentedStats();
          console.log('📊 Hermes Memory Stats:', stats);
        }
      }
    } catch (error) {
      console.error('❌ Error getting Hermes info:', error);
    }
  }

  /**
   * Enhanced logging with persistence for production debugging
   */
  static log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      data,
      platform: Platform.OS,
      isProduction: this.isProduction,
      hermesEnabled: this.isHermesEnabled
    };

    // Always log to console
    const consoleMessage = `[${timestamp}] ${level.toUpperCase()}: ${message}`;
    switch (level) {
      case 'error':
        console.error(consoleMessage, data);
        break;
      case 'warn':
        console.warn(consoleMessage, data);
        break;
      case 'info':
        console.info(consoleMessage, data);
        break;
      default:
        console.log(consoleMessage, data);
    }

    // Store in memory for debugging
    this.debugLogs.push(logEntry);
    
    // Keep only the last N entries
    if (this.debugLogs.length > this.maxLogEntries) {
      this.debugLogs = this.debugLogs.slice(-this.maxLogEntries);
    }

    // Persist critical logs in production
    if (this.isProduction && (level === 'error' || level === 'warn')) {
      this.persistLog(logEntry);
    }
  }

  /**
   * Persist logs to AsyncStorage for debugging
   */
  static async persistLog(logEntry) {
    try {
      const key = `debug_log_${Date.now()}`;
      await AsyncStorage.setItem(key, JSON.stringify(logEntry));
      
      // Clean up old logs (keep only last 50)
      const allKeys = await AsyncStorage.getAllKeys();
      const debugKeys = allKeys.filter(key => key.startsWith('debug_log_')).sort();
      
      if (debugKeys.length > 50) {
        const keysToRemove = debugKeys.slice(0, debugKeys.length - 50);
        await AsyncStorage.multiRemove(keysToRemove);
      }
    } catch (error) {
      console.error('❌ Failed to persist log:', error);
    }
  }

  /**
   * Get all stored debug logs
   */
  static async getStoredLogs() {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const debugKeys = allKeys.filter(key => key.startsWith('debug_log_')).sort();
      
      const logs = [];
      for (const key of debugKeys) {
        const logData = await AsyncStorage.getItem(key);
        if (logData) {
          logs.push(JSON.parse(logData));
        }
      }
      
      return logs;
    } catch (error) {
      console.error('❌ Failed to get stored logs:', error);
      return [];
    }
  }

  /**
   * Export logs for debugging
   */
  static async exportLogs() {
    try {
      const storedLogs = await this.getStoredLogs();
      const allLogs = {
        memoryLogs: this.debugLogs,
        persistedLogs: storedLogs,
        systemInfo: {
          platform: Platform.OS,
          isProduction: this.isProduction,
          hermesEnabled: this.isHermesEnabled,
          timestamp: new Date().toISOString()
        }
      };
      
      console.log('📋 Debug logs exported:', allLogs);
      return allLogs;
    } catch (error) {
      console.error('❌ Failed to export logs:', error);
      return null;
    }
  }

  /**
   * Clear all debug logs
   */
  static async clearLogs() {
    try {
      this.debugLogs = [];
      
      const allKeys = await AsyncStorage.getAllKeys();
      const debugKeys = allKeys.filter(key => key.startsWith('debug_log_'));
      
      if (debugKeys.length > 0) {
        await AsyncStorage.multiRemove(debugKeys);
      }
      
      console.log('🧹 Debug logs cleared');
    } catch (error) {
      console.error('❌ Failed to clear logs:', error);
    }
  }

  /**
   * Setup crash reporting
   */
  static setupCrashReporting() {
    // Global error handler
    if (global.ErrorUtils) {
      const originalHandler = global.ErrorUtils.getGlobalHandler();
      
      global.ErrorUtils.setGlobalHandler((error, isFatal) => {
        this.log('error', 'Global JavaScript Error', {
          message: error.message,
          stack: error.stack,
          isFatal,
          name: error.name
        });
        
        // Call original handler
        if (originalHandler) {
          originalHandler(error, isFatal);
        }
      });
    }

    // Unhandled promise rejections
    if (typeof global !== 'undefined' && global.addEventListener) {
      global.addEventListener('unhandledrejection', (event) => {
        this.log('error', 'Unhandled Promise Rejection', {
          reason: event.reason,
          promise: event.promise
        });
      });
    }
  }

  /**
   * Setup performance monitoring
   */
  static setupPerformanceMonitoring() {
    // Monitor memory usage periodically in production
    if (this.isProduction && this.isHermesEnabled) {
      setInterval(() => {
        try {
          if (global.HermesInternal?.getInstrumentedStats) {
            const stats = global.HermesInternal.getInstrumentedStats();
            this.log('info', 'Memory Stats', stats);
          }
        } catch (error) {
          this.log('error', 'Failed to get memory stats', error);
        }
      }, 60000); // Every minute
    }
  }

  /**
   * Capture and log React component errors
   */
  static logComponentError(error, errorInfo) {
    this.log('error', 'React Component Error', {
      error: {
        message: error.message,
        stack: error.stack,
        name: error.name
      },
      errorInfo
    });
  }

  /**
   * Log WebView errors specifically
   */
  static logWebViewError(error, context = '') {
    this.log('error', `WebView Error ${context}`, {
      error: error.message || error,
      context,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Log network errors
   */
  static logNetworkError(error, url = '', method = '') {
    this.log('error', 'Network Error', {
      error: error.message || error,
      url,
      method,
      timestamp: new Date().toISOString()
    });
  }
}

// Convenience methods
export const debugLog = (message, data) => DebugUtils.log('debug', message, data);
export const infoLog = (message, data) => DebugUtils.log('info', message, data);
export const warnLog = (message, data) => DebugUtils.log('warn', message, data);
export const errorLog = (message, data) => DebugUtils.log('error', message, data);

export default DebugUtils;
