{"name": "myguc", "version": "1.0.0", "main": "index.js", "private": true, "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "debug:setup": "node scripts/debug-hermes.js --setup", "debug:logs": "node scripts/debug-hermes.js --extract-logs", "debug:symbolicate": "node scripts/debug-hermes.js --symbolicate", "build:debug": "npx eas build --profile debug --platform android --clear-cache", "build:preview": "npx eas build --profile preview --platform android --clear-cache", "build:production": "npx eas build --profile production --platform android --clear-cache", "build:ios:debug": "npx eas build --profile debug --platform ios --clear-cache", "build:ios:preview": "npx eas build --profile preview --platform ios --clear-cache", "build:ios:production": "npx eas build --profile production --platform ios --clear-cache"}, "dependencies": {"@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "expo": "^52.0.0", "expo-build-properties": "~0.13.3", "expo-dev-client": "~5.0.20", "expo-status-bar": "~2.0.1", "patch-package": "^8.0.0", "postinstall-postinstall": "^2.1.0", "react": "18.3.1", "react-native": "0.76.9", "react-native-gesture-handler": "~2.20.2", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.24.0"}}