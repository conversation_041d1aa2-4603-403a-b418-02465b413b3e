import React from 'react';
import { Animated } from 'react-native';
import { useTheme } from '../contexts/ThemeContext';

/**
 * Higher-order component that wraps screens with smooth theme transition animations
 * This provides a consistent animation experience across the entire app when themes change
 */
const ThemeTransitionWrapper = ({ children, style = {} }) => {
  const { transitionAnim, fadeAnim } = useTheme();

  return (
    <Animated.View
      style={[
        { flex: 1 },
        style,
        {
          transform: [{ scale: transitionAnim }],
          opacity: fadeAnim,
        },
      ]}
    >
      {children}
    </Animated.View>
  );
};

export default ThemeTransitionWrapper;
