import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  Animated,
  StyleSheet,
  Easing,
  Dimensions,
  Linking,
  Alert,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../contexts/ThemeContext';
import { useOffline } from '../contexts/OfflineContext';
import Svg, { Path, Line, G, ClipPath, Defs } from 'react-native-svg';
import { hasCachedData, showOfflineUnavailablePopup } from '../utils/OfflineUtils';
import { isDemoMode } from '../utils/DemoModeHelper';

const Sidebar = ({
  isVisible,
  onClose,
  sidebarAnim,
  navigation,
  currentScreen = 'Home'
}) => {
  // Theme context
  const { theme, transitionAnim, fadeAnim, switchTheme, currentThemeName } = useTheme();

  // Offline context
  const { isOfflineMode, isFeatureAvailable, exitOfflineMode, checkNetworkConnectivity } = useOffline();

  const [studentInfo, setStudentInfo] = useState({
    fullName: '',
    studentId: '',
    faculty: ''
  });

  const [isLoadingStudentInfo, setIsLoadingStudentInfo] = useState(true);
  const [isThemeMenuVisible, setIsThemeMenuVisible] = useState(false);
  const [themeMenuAnim] = useState(new Animated.Value(0));

  // State for tracking demo mode
  const [isCurrentlyDemoMode, setIsCurrentlyDemoMode] = useState(false);

  // Check for demo mode on component mount
  const [cachedDataAvailability, setCachedDataAvailability] = useState({
    grades: false,
    attendance: false,
    schedule: false,
    exams: false,
    transcript: false,
    financials: false
  });

  // Animation refs for enhanced menu interactions
  const menuItemAnims = useRef({}).current;
  const menuItemScales = useRef({}).current;

  // Theme Icon Component
  const ThemeIcon = ({ size = 20, color = theme.colors.textSecondary }) => (
    <Svg width={size} height={size} viewBox="0 0 192 192" fill="none">
      <G clipPath="url(#a)">
        <Path 
          stroke={color} 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth="8" 
          d="M96 22v30m24-30v36m-42 76v24a11.998 11.998 0 0 0 12 12h12c3.183 0 6.235-1.264 8.485-3.515A11.996 11.996 0 0 0 114 158v-24M48 96v20a11.998 11.998 0 0 0 12 12h72c3.183 0 6.235-1.264 8.485-3.515A11.996 11.996 0 0 0 144 116V96.149L48 96Zm0 0V46a24 24 0 0 1 24-24h72v74H48Z"
        />
      </G>
      <Defs>
        <ClipPath id="a">
          <Path fill="#ffffff" d="M0 0h192v192H0z"/>
        </ClipPath>
      </Defs>
    </Svg>
  );

  // Theme toggle function
  const toggleThemeMenu = () => {
    if (isThemeMenuVisible) {
      // Close theme menu
      Animated.timing(themeMenuAnim, {
        toValue: 0,
        duration: 200,
        useNativeDriver: false,
      }).start(() => {
        setIsThemeMenuVisible(false);
      });
    } else {
      // Open theme menu
      setIsThemeMenuVisible(true);
      Animated.timing(themeMenuAnim, {
        toValue: 1,
        duration: 200,
        useNativeDriver: false,
      }).start();
    }
  };

  // Handle theme selection
  const handleThemeSelection = (themeName) => {
    console.log(`🎨 Theme selected: ${themeName}`);
    switchTheme(themeName);
    
    // Close theme menu after selection
    setTimeout(() => {
      toggleThemeMenu();
    }, 100);
  };

  // Get theme preview colors
  const getThemePreview = (themeName) => {
    switch (themeName) {
      case 'dark':
        return { bg: '#2C2C2C', primary: '#EAB308', text: '#FFFFFF' };
      case 'light':
        return { bg: '#FFFFFF', primary: '#EAB308', text: '#212529' };
      case 'colorful':
        return { bg: '#FF69B4', primary: '#FFD700', text: '#FFFFFF' };
      case 'navy':
        return { bg: '#1B2951', primary: '#DC2626', text: '#FFFFFF' };
      default:
        return { bg: '#2C2C2C', primary: '#EAB308', text: '#FFFFFF' };
    }
  };

  // Theme option component
  const ThemeOption = ({ themeName, displayName }) => {
    const themeColors = getThemePreview(themeName);
    const isSelected = currentThemeName === themeName;
    
    return (
      <TouchableOpacity
        style={[
          styles.themeOption,
          isSelected && styles.themeOptionSelected,
          { backgroundColor: themeColors.bg }
        ]}
        onPress={() => handleThemeSelection(themeName)}
        activeOpacity={0.7}
      >
        <View style={[styles.themePreview, { backgroundColor: themeColors.bg }]}>
          <View style={[styles.themePreviewDot, { backgroundColor: themeColors.primary }]} />
        </View>
        <Text style={[styles.themeOptionText, { color: themeColors.text }]}>
          {displayName}
        </Text>
        {isSelected && (
          <View style={[styles.themeSelectedIndicator, { backgroundColor: themeColors.primary }]} />
        )}
      </TouchableOpacity>
    );
  };

  // Initialize animations for menu items
  const menuItems = ['Home', 'Grades', 'Exams', 'Schedule', 'Attendance', 'Transcript', 'Evaluate', 'Financials'];

  useEffect(() => {
    menuItems.forEach((item, index) => {
      if (!menuItemAnims[item]) {
        menuItemAnims[item] = new Animated.Value(isVisible ? 0 : 1); // Start visible if sidebar is already open
        menuItemScales[item] = new Animated.Value(1);
      }
    });
  }, []);

  // Fallback to ensure menu items are visible if animations fail
  useEffect(() => {
    const fallbackTimer = setTimeout(() => {
      if (isVisible) {
        menuItems.forEach(item => {
          if (menuItemAnims[item]) {
            menuItemAnims[item].setValue(1);
          }
        });
      }
    }, 1000); // Fallback after 1 second

    return () => clearTimeout(fallbackTimer);
  }, [isVisible]);

  // Check for demo mode on component mount
  useEffect(() => {
    const checkDemoMode = async () => {
      const isDemo = await isDemoMode();
      setIsCurrentlyDemoMode(isDemo);
      console.log('🎭 Sidebar: Demo mode check result:', isDemo);
    };
    
    if (isVisible) {
      checkDemoMode();
    }
  }, [isVisible]);

  // Check cached data availability when sidebar opens
  useEffect(() => {
    if (isVisible && isOfflineMode) {
      checkCachedDataAvailability();
    }
  }, [isVisible, isOfflineMode]);

  // Calculate responsive scaling factors at component level
  const { width, height } = Dimensions.get('window');
  const isVerySmallScreen = height < 600;
  const isSmallScreen = height < 700;
  const isMediumScreen = height < 800;

  // Calculate scaling factors at component level so they can be used in both JSX and styles
  const screenHeight = height;
  
  // Define proportional allocations that scale with screen size
  let userSectionRatio, menuSectionRatio, footerSectionRatio;
  
  if (isVerySmallScreen) {
    userSectionRatio = 0.20; // 20%
    menuSectionRatio = 0.55;  // 55%
    footerSectionRatio = 0.25; // 25%
  } else if (isSmallScreen) {
    userSectionRatio = 0.22; // 22%
    menuSectionRatio = 0.53;  // 53%
    footerSectionRatio = 0.25; // 25%
  } else if (isMediumScreen) {
    userSectionRatio = 0.25; // 25%
    menuSectionRatio = 0.50;  // 50%
    footerSectionRatio = 0.25; // 25%
  } else {
    userSectionRatio = 0.28; // 28%
    menuSectionRatio = 0.47;  // 47%
    footerSectionRatio = 0.25; // 25%
  }

  // Calculate actual heights for each section
  const availableHeight = screenHeight - 100;
  const userSectionHeight = availableHeight * userSectionRatio;
  const menuSectionHeight = availableHeight * menuSectionRatio;
  const footerSectionHeight = availableHeight * footerSectionRatio;

  // Dynamic scaling factors
  const userInfoScale = isVerySmallScreen ? 0.75 : isSmallScreen ? 0.85 : isMediumScreen ? 0.95 : 1.1;
  const menuItemScale = isVerySmallScreen ? 0.7 : isSmallScreen ? 0.8 : isMediumScreen ? 0.9 : 1.0;
  const footerScale = isVerySmallScreen ? 0.7 : isSmallScreen ? 0.8 : isMediumScreen ? 0.9 : 1.0;

  // Calculate menu item sizing
  const totalMenuItems = 8;
  const targetItemHeight = menuSectionHeight / totalMenuItems;
  const minItemHeight = 32;
  const maxItemHeight = 50;
  const finalItemHeight = Math.max(minItemHeight, Math.min(maxItemHeight, targetItemHeight));
  
  // Font scaling based on section allocations
  const baseFontScale = isVerySmallScreen ? 0.8 : isSmallScreen ? 0.85 : isMediumScreen ? 0.95 : 1.0;
  const userFontScale = baseFontScale * userInfoScale;
  const menuFontScale = baseFontScale * menuItemScale;
  const footerFontScale = baseFontScale * footerScale;

  // Generate styles based on current theme
  const styles = createStyles(theme, {
    userSectionHeight,
    menuSectionHeight,
    footerSectionHeight,
    userInfoScale,
    menuItemScale,
    footerScale,
    finalItemHeight,
    userFontScale,
    menuFontScale,
    footerFontScale,
    isVerySmallScreen,
    isSmallScreen,
    isMediumScreen,
    width
  });

  useEffect(() => {
    if (isVisible) {
      loadStudentInfo();
      // Small delay to ensure sidebar is visible before animating items
      setTimeout(() => {
        animateMenuItemsIn();
      }, 100);
    } else {
      // Reset animations when sidebar closes
      resetMenuAnimations();
    }
  }, [isVisible]);

  // Animate menu items in with staggered effect - More stable version
  const animateMenuItemsIn = () => {
    // Reset all animations first to ensure clean state
    menuItems.forEach(item => {
      if (menuItemAnims[item]) {
        menuItemAnims[item].setValue(0);
      }
      if (menuItemScales[item]) {
        menuItemScales[item].setValue(1);
      }
    });

    // Then animate them in with shorter delays for stability
    menuItems.forEach((item, index) => {
      if (menuItemAnims[item]) {
        Animated.sequence([
          Animated.delay(index * 30), // Reduced delay for more stability
          Animated.timing(menuItemAnims[item], {
            toValue: 1,
            duration: 250, // Shorter duration for stability
            easing: Easing.out(Easing.ease), // Simpler easing
            useNativeDriver: true,
          })
        ]).start();
      }
    });
  };

  // Reset menu animations
  const resetMenuAnimations = () => {
    menuItems.forEach(item => {
      if (menuItemAnims[item]) {
        menuItemAnims[item].setValue(0);
      }
      if (menuItemScales[item]) {
        menuItemScales[item].setValue(1);
      }
    });
  };

  // Enhanced menu item press animation
  const handleMenuItemPress = (itemName, onPress) => {
    // Scale down animation
    Animated.sequence([
      Animated.timing(menuItemScales[itemName], {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(menuItemScales[itemName], {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      })
    ]).start();

    // Call the original onPress after a short delay for visual feedback
    setTimeout(() => {
      onPress();
    }, 50);
  };

  // Enhanced Menu Item Component - More stable version
  const EnhancedMenuItem = ({ title, onPress, isActive }) => {
    // Ensure animations exist with fallbacks
    const animValue = menuItemAnims[title] || new Animated.Value(1);
    const scaleValue = menuItemScales[title] || new Animated.Value(1);

    // Check if this item should be dimmed in offline mode
    const isDimmed = isOfflineMode && (
      (title === 'Evaluate') || // Always dimmed in offline mode
      (title === 'Grades' && !cachedDataAvailability.grades) ||
      (title === 'Attendance' && !cachedDataAvailability.attendance) ||
      (title === 'Schedule' && !cachedDataAvailability.schedule) ||
      (title === 'Exams' && !cachedDataAvailability.exams) ||
      (title === 'Transcript' && !cachedDataAvailability.transcript) ||
      (title === 'Financials' && !cachedDataAvailability.financials)
    );

    const translateY = animValue.interpolate({
      inputRange: [0, 1],
      outputRange: [15, 0], // Reduced movement for stability
    });

    const opacity = animValue;

    return (
      <Animated.View
        style={[
          styles.menuItem,
          {
            transform: [
              { translateY },
              { scale: scaleValue }
            ],
            opacity,
          }
        ]}
      >
        <TouchableOpacity
          style={[
            styles.menuItemTouchable,
            isActive && styles.menuItemActive,
            isDimmed && styles.menuItemDimmed
          ]}
          onPress={() => handleMenuItemPress(title, onPress)}
          activeOpacity={isDimmed ? 0.3 : 0.7}
        >
          <Text style={[
            styles.menuItemText,
            isActive && styles.menuItemActiveText,
            isDimmed && styles.menuItemDimmedText
          ]}>
            {title}
          </Text>
          {isActive && <View style={styles.activeIndicator} />}
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Function to decode HTML entities
  const decodeHtmlEntities = (text) => {
    if (!text) return text;

    return text
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&nbsp;/g, ' ')
      .replace(/&#(\d+);/g, (match, dec) => String.fromCharCode(dec))
      .replace(/&#x([0-9a-f]+);/gi, (match, hex) => String.fromCharCode(parseInt(hex, 16)));
  };

  const loadStudentInfo = async () => {
    try {
      setIsLoadingStudentInfo(true);

      const storedFullName = await AsyncStorage.getItem('student_fullName');
      const storedStudentId = await AsyncStorage.getItem('student_id');
      const storedFaculty = await AsyncStorage.getItem('student_faculty');

      setStudentInfo({
        fullName: storedFullName ? decodeHtmlEntities(storedFullName) : '',
        studentId: storedStudentId ? decodeHtmlEntities(storedStudentId) : '',
        faculty: storedFaculty ? decodeHtmlEntities(storedFaculty) : ''
      });
    } catch (error) {
      console.log('Error loading student info from localStorage:', error);
    } finally {
      setIsLoadingStudentInfo(false);
    }
  };


  const handleFeedback = async () => {
    try {
      const subject = 'MyGUC App Feedback';
      const body = 'Hi MyGUC Team,\n\nI would like to share the following feedback about the app:\n\n';
      const emailUrl = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

      const canOpen = await Linking.canOpenURL(emailUrl);
      if (canOpen) {
        await Linking.openURL(emailUrl);
        console.log('📧 Feedback email opened');
      } else {
        console.log('❌ Cannot open email client');
        // Fallback: copy email to clipboard or show alert
      }
    } catch (error) {
      console.error('Error opening feedback email:', error);
    }
  };

  // Check cached data availability
  const checkCachedDataAvailability = async () => {
    try {
      const availability = {
        grades: await hasCachedData('grades'),
        attendance: await hasCachedData('attendance'),
        schedule: await hasCachedData('schedule'),
        exams: await hasCachedData('exams'),
        transcript: await hasCachedData('transcript'),
        financials: await hasCachedData('financials')
      };

      setCachedDataAvailability(availability);
      console.log('📦 Cached data availability:', availability);
    } catch (error) {
      console.log('❌ Error checking cached data availability:', error);
    }
  };

  // Handle "Go Online" button press
  const handleGoOnline = async () => {
    try {
      console.log('🌐 Attempting to go online...');

      // Skip network check if in demo mode - demo accounts shouldn't trigger network checks
      if (isCurrentlyDemoMode) {
        console.log('🎭 Demo mode detected - skipping network check and go online functionality');
        Alert.alert(
          'Demo Mode',
          'This is a demo account. Go Online functionality is not available in demo mode.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Check network connectivity first
      const hasConnection = await checkNetworkConnectivity();
      if (!hasConnection) {
        Alert.alert(
          'No Connection',
          'Please check your internet connection and try again.',
          [{ text: 'OK' }]
        );
        return;
      }

      // Get stored credentials
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        Alert.alert(
          'No Credentials',
          'No stored credentials found. Please login again.',
          [
            {
              text: 'Login',
              onPress: () => {
                onClose();
                navigation.reset({
                  index: 0,
                  routes: [{ name: 'Login' }],
                });
              }
            },
            { text: 'Cancel' }
          ]
        );
        return;
      }

      // Close sidebar and exit offline mode
      onClose();
      await exitOfflineMode();

      // Navigate to login screen to attempt auto-login
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });

    } catch (error) {
      console.log('❌ Error going online:', error);
      Alert.alert('Error', 'Failed to go online. Please try again.');
    }
  };

  const handleLogout = async () => {
    try {
      // Clear all stored data
      await AsyncStorage.clear();
      console.log('🚪 Logged out - All data cleared');

      // Close sidebar first
      onClose();

      // Navigate back to login screen
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  const handleHomePress = async () => {
    try {
      console.log('🏠 Navigating to Home...');

      // Close sidebar first
      onClose();

      // Reset navigation to Portal screen to ensure clean state
      navigation.reset({
        index: 0,
        routes: [{ name: 'Portal' }],
      });
    } catch (error) {
      console.error('Error navigating to home:', error);
    }
  };

  const handleGradesPress = async () => {
    try {
      console.log('📚 Navigating to Grades...');

      // Check if feature is available in offline mode
      if (isOfflineMode && !cachedDataAvailability.grades) {
        showOfflineUnavailablePopup(Alert.alert, 'Grades');
        return;
      }

      // Close sidebar first
      onClose();

      // Navigate to Grades screen
      navigation.navigate('Grades');
    } catch (error) {
      console.error('Error navigating to grades:', error);
    }
  };

  const handleExamsPress = async () => {
    try {
      console.log('📝 Navigating to Exams...');

      // Check if feature is available in offline mode
      if (isOfflineMode && !cachedDataAvailability.exams) {
        showOfflineUnavailablePopup(Alert.alert, 'Exams');
        return;
      }

      // Close sidebar first
      onClose();

      // Navigate to Exams screen
      navigation.navigate('Exams');
    } catch (error) {
      console.error('Error navigating to exams:', error);
    }
  };

  const handleSchedulePress = async () => {
    try {
      console.log('📅 Navigating to Schedule...');

      // Check if feature is available in offline mode
      if (isOfflineMode && !cachedDataAvailability.schedule) {
        showOfflineUnavailablePopup(Alert.alert, 'Schedule');
        return;
      }

      // Close sidebar first
      onClose();

      navigation.navigate('Schedule');
    } catch (error) {
      console.error('Error navigating to schedule:', error);
    }
  };

  const handleAttendancePress = async () => {
    try {
      console.log('📊 Navigating to Attendance...');

      // Check if feature is available in offline mode
      if (isOfflineMode && !cachedDataAvailability.attendance) {
        showOfflineUnavailablePopup(Alert.alert, 'Attendance');
        return;
      }

      // Close sidebar first
      onClose();

      navigation.navigate('Attendance');
    } catch (error) {
      console.error('Error navigating to attendance:', error);
    }
  };

  const handleFinancialsPress = async () => {
    try {
      console.log('💰 Navigating to Financials...');

      // Check if feature is available in offline mode
      if (isOfflineMode && !cachedDataAvailability.financials) {
        showOfflineUnavailablePopup(Alert.alert, 'Financials');
        return;
      }

      // Close sidebar first
      onClose();

      navigation.navigate('Financials');
    } catch (error) {
      console.error('Error navigating to financials:', error);
    }
  };

  const handleTranscriptPress = async () => {
    try {
      console.log('📜 Navigating to Transcript...');

      // Check if feature is available in offline mode
      if (isOfflineMode && !cachedDataAvailability.transcript) {
        showOfflineUnavailablePopup(Alert.alert, 'Transcript');
        return;
      }

      // Close sidebar first
      onClose();

      navigation.navigate('Transcript');
    } catch (error) {
      console.error('Error navigating to transcript:', error);
    }
  };

  const handleEvaluatePress = async () => {
    try {
      console.log('⭐ Navigating to Evaluate...');

      // Evaluate is always disabled in offline mode
      if (isOfflineMode) {
        showOfflineUnavailablePopup(Alert.alert, 'Evaluate');
        return;
      }

      // Close sidebar first
      onClose();

      navigation.navigate('Evaluate');
    } catch (error) {
      console.error('Error navigating to evaluate:', error);
    }
  };

  if (!isVisible) return null;

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <TouchableOpacity
        style={styles.overlay}
        activeOpacity={1}
        onPress={onClose}
      >
        <Animated.View
          style={[
            styles.container,
            {
              transform: [
                { translateX: sidebarAnim },
                { scale: transitionAnim }
              ],
              opacity: fadeAnim
            }
          ]}
        >
          <View style={styles.content}>
            {/* User Info */}
            <View style={styles.userSection}>
              <View style={styles.userInfo}>
                {isLoadingStudentInfo ? (
                  <>
                    {/* Ghost loading for name */}
                    <View style={[styles.ghostLoading, styles.ghostName]} />
                    {/* Ghost loading for student ID */}
                    <View style={[styles.ghostLoading, styles.ghostId]} />
                    {/* Ghost loading for faculty */}
                    <View style={[styles.ghostLoading, styles.ghostFaculty]} />
                  </>
                ) : (
                  <>
                    <Text style={styles.userName}>{studentInfo.fullName && typeof studentInfo.fullName === 'string' && studentInfo.fullName.trim() ? (() => {
                      const nameParts = studentInfo.fullName.trim().split(' ').filter(part => part.length > 0);
                      if (nameParts.length === 1) return nameParts[0];
                      if (nameParts.length >= 2) return `${nameParts[0]} ${nameParts[nameParts.length - 1]}`;
                      return studentInfo.fullName;
                    })() : 'Hello Student'}</Text>
                    <Text style={styles.userId}>{studentInfo.studentId || 'Loading...'}</Text>
                    <Text style={styles.userFaculty}>{studentInfo.faculty || 'Loading...'}</Text>
                    {/* iOS-compatible line separator */}
                    <View style={styles.facultyLineSeparator} />
                  </>
                )}
              </View>
            </View>

            {/* Enhanced Menu Items - Dynamic Scaling */}
            <View style={styles.menu}>
              <EnhancedMenuItem
                title="Home"
                onPress={handleHomePress}
                isActive={currentScreen === 'Home'}
              />
              <EnhancedMenuItem
                title="Grades"
                onPress={handleGradesPress}
                isActive={currentScreen === 'Grades'}
              />
              <EnhancedMenuItem
                title="Exams"
                onPress={handleExamsPress}
                isActive={currentScreen === 'Exams'}
              />
              <EnhancedMenuItem
                title="Schedule"
                onPress={handleSchedulePress}
                isActive={currentScreen === 'Schedule'}
              />
              <EnhancedMenuItem
                title="Attendance"
                onPress={handleAttendancePress}
                isActive={currentScreen === 'Attendance'}
              />
              <EnhancedMenuItem
                title="Transcript"
                onPress={handleTranscriptPress}
                isActive={currentScreen === 'Transcript'}
              />
              <EnhancedMenuItem
                title="Evaluate"
                onPress={handleEvaluatePress}
                isActive={currentScreen === 'Evaluate'}
              />
              <EnhancedMenuItem
                title="Financials"
                onPress={handleFinancialsPress}
                isActive={currentScreen === 'Financials'}
              />
            </View>

            {/* Feedback and Logout */}
            <View style={styles.footer}>
              {/* Theme Selector */}
              <View style={styles.themeContainer}>
                <TouchableOpacity style={styles.themeButton} onPress={toggleThemeMenu}>
                  <View style={styles.themeIconContainer}>
                    <ThemeIcon size={Math.max(18, 22 * footerFontScale)} color={theme.colors.textSecondary} />
                  </View>
                  <Text style={styles.themeText}>Theme</Text>
                </TouchableOpacity>
              </View>

              <TouchableOpacity style={styles.feedbackButton} onPress={handleFeedback}>
                <View style={styles.feedbackIconContainer}>
                  {/* Chat/Feedback SVG Icon */}
                  <Svg width={Math.max(12, 15 * footerFontScale)} height={Math.max(12, 15 * footerFontScale)} viewBox="0 0 24 24" fill="none">
                    <Path
                      d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"
                      stroke={theme.colors.textSecondary}
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      fill="none"
                    />
                    <Line x1="7" y1="10" x2="17" y2="10" stroke={theme.colors.textSecondary} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                    <Line x1="7" y1="14" x2="15" y2="14" stroke={theme.colors.textSecondary} strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </Svg>
                </View>
                <Text style={styles.feedbackText}>Feedback</Text>
              </TouchableOpacity>

              {/* Go Online button - only show in offline mode AND not in demo mode */}
              {isOfflineMode && !isCurrentlyDemoMode && (
                <TouchableOpacity style={styles.goOnlineButton} onPress={handleGoOnline}>
                  <Text style={styles.goOnlineIcon}>🌐</Text>
                  <Text style={styles.goOnlineText}>Go Online</Text>
                </TouchableOpacity>
              )}

              <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                <Text style={styles.logoutIcon}>↗</Text>
                <Text style={styles.logoutText}>Logout</Text>
              </TouchableOpacity>
            </View>
          </View>
        </Animated.View>
        
        {/* Theme Options Menu - Positioned as overlay on top of sidebar */}
        {isThemeMenuVisible && (
          <TouchableOpacity 
            style={styles.themeMenuOverlay}
            activeOpacity={1}
            onPress={(e) => {
              e.stopPropagation();
              toggleThemeMenu();
            }}
          >
            <TouchableOpacity
              style={styles.themeMenuContainer}
              activeOpacity={1}
              onPress={(e) => e.stopPropagation()}
            >
              <Animated.View
                style={[
                  styles.themeMenu,
                  {
                    opacity: themeMenuAnim,
                    transform: [{
                      translateY: themeMenuAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [-10, 0],
                      })
                    }, {
                      scale: themeMenuAnim.interpolate({
                        inputRange: [0, 1],
                        outputRange: [0.95, 1],
                      })
                    }]
                  }
                ]}
              >
                <View style={styles.themeMenuHeader}>
                  <Text style={styles.themeMenuTitle}>Select Theme</Text>
                  <TouchableOpacity
                    style={styles.themeMenuClose}
                    onPress={toggleThemeMenu}
                  >
                    <Text style={styles.themeMenuCloseText}>×</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.themeOptionsContainer}>
                  <ThemeOption themeName="dark" displayName="Dark" />
                  <ThemeOption themeName="light" displayName="Light" />
                  <ThemeOption themeName="colorful" displayName="Pink" />
                  <ThemeOption themeName="navy" displayName="Navy" />
                </View>
              </Animated.View>
            </TouchableOpacity>
          </TouchableOpacity>
        )}
      </TouchableOpacity>
    </Modal>
  );
};

// Create styles function that uses theme and screen dimensions
const createStyles = (theme, scaleParams) => {
  const {
    userSectionHeight,
    menuSectionHeight,
    footerSectionHeight,
    userInfoScale,
    menuItemScale,
    footerScale,
    finalItemHeight,
    userFontScale,
    menuFontScale,
    footerFontScale,
    isVerySmallScreen,
    isSmallScreen,
    isMediumScreen,
    width
  } = scaleParams;

  // Optimized width scaling for better proportions
  const containerWidth = isVerySmallScreen
    ? Math.min(260, width * 0.72)
    : isSmallScreen
      ? Math.min(270, width * 0.74)
      : isMediumScreen
        ? Math.min(285, width * 0.76)
        : Math.min(300, width * 0.78);

  return StyleSheet.create({
    overlay: {
      flex: 1,
      backgroundColor: theme.colors.modalOverlay,
      justifyContent: 'flex-start',
      paddingTop: 0, // Remove any top padding to ensure full height
    },
    container: {
      width: containerWidth,
      height: '100%',
      backgroundColor: theme.colors.surface,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 2, height: 0 },
      shadowOpacity: 0.25,
      shadowRadius: 3.84,
      elevation: 5,
    },
    content: {
      flex: 1,
      paddingTop: Math.max(15, 20 * (isVerySmallScreen ? 0.7 : isSmallScreen ? 0.8 : 1.0)),
      paddingHorizontal: Math.max(12, 16 * (isVerySmallScreen ? 0.8 : isSmallScreen ? 0.9 : 1.0)),
      paddingBottom: Math.max(15, 20 * (isVerySmallScreen ? 0.7 : isSmallScreen ? 0.8 : 1.0)),
    },
    header: {
      marginBottom: Math.max(25, 30 * userInfoScale),
      alignItems: 'flex-start',
    },
    timeText: {
      fontSize: Math.max(20, 24 * userFontScale),
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    userSection: {
      flexDirection: 'row',
      alignItems: 'center',
      height: userSectionHeight,
      paddingVertical: Math.max(4, 6 * userInfoScale), // Reduced vertical padding even more
      paddingBottom: 0, // Removed bottom padding completely to eliminate gap
      borderBottomWidth: 0, // Removed border to eliminate visual separation
      borderBottomColor: theme.colors.border,
      justifyContent: 'center',
    },
    userIcon: {
      width: Math.max(35, 45 * userInfoScale),
      height: Math.max(35, 45 * userInfoScale),
      borderRadius: Math.max(17, 22 * userInfoScale),
      backgroundColor: theme.colors.border,
      justifyContent: 'center',
      alignItems: 'center',
      marginRight: Math.max(8, 12 * userInfoScale),
    },
    userIconText: {
      fontSize: Math.max(16, 20 * userFontScale),
      color: theme.colors.primary,
    },
    userInfo: {
      flex: 1,
    },
    userName: {
      fontSize: Math.max(14, 18 * userFontScale),
      fontWeight: '800',
      color: theme.colors.text,
      marginBottom: Math.max(2, 4 * userInfoScale),
      letterSpacing: 0.3,
      lineHeight: Math.max(16, 20 * userFontScale),
    },
    userId: {
      fontSize: Math.max(11, 13 * userFontScale),
      fontWeight: '500',
      color: theme.colors.textSecondary,
      marginBottom: Math.max(1, 2 * userInfoScale),
      letterSpacing: 0.2,
      lineHeight: Math.max(13, 15 * userFontScale),
    },
    userFaculty: {
      fontSize: Math.max(10, 12 * userFontScale),
      fontWeight: '500',
      color: theme.colors.textSecondary,
      letterSpacing: 0.1,
      lineHeight: Math.max(12, 14 * userFontScale),
      paddingBottom: Math.max(8, 10 * userInfoScale),
      marginBottom: Math.max(8, 10 * userInfoScale),
    },
    // iOS-compatible line separator (View element instead of border)
    facultyLineSeparator: {
      height: 2,
      backgroundColor: theme.colors.border, // Changed from textSecondary to match footer border
      width: '100%',
      marginTop: Math.max(8, 10 * userInfoScale),
      marginBottom: Math.max(4, 6 * userInfoScale),
      opacity: 1, // Changed from 0.8 to match footer line opacity
    },
    // Ghost loading styles
    ghostLoading: {
      backgroundColor: theme.colors.textSecondary,
      opacity: 0.3,
      borderRadius: 4,
      marginBottom: Math.max(3, 5 * userInfoScale),
    },
    ghostName: {
      height: Math.max(14, 18 * userInfoScale),
      width: '80%',
    },
    ghostId: {
      height: Math.max(11, 13 * userInfoScale),
      width: '60%',
    },
    ghostFaculty: {
      height: Math.max(10, 12 * userInfoScale),
      width: '90%',
      marginBottom: 0,
    },
    menu: {
      height: menuSectionHeight,
      marginTop: Math.max(-35, -45 * menuItemScale), // Reduced negative margin to lower the menu section
      marginBottom: Math.max(12, 18 * menuItemScale),
    },
    menuItem: {
      marginBottom: Math.max(6, 10 * menuItemScale), // Increased from 2,4 to 6,10 for greater gap
    },
    menuItemTouchable: {
      height: finalItemHeight,
      paddingVertical: Math.max(4, 6 * menuItemScale),
      paddingHorizontal: Math.max(6, 10 * menuItemScale),
      borderRadius: Math.max(4, 6 * menuItemScale),
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      backgroundColor: 'transparent',
    },
    menuItemActive: {
      backgroundColor: theme.colors.primary + '15', // 15% opacity
      borderLeftWidth: Math.max(3, 4 * menuItemScale),
      borderLeftColor: theme.colors.primary,
    },
    menuItemText: {
      fontSize: Math.max(15, 18 * menuFontScale), // Increased from 12,15 to 15,18
      fontWeight: '600',
      color: theme.colors.text,
      letterSpacing: 0.3,
      lineHeight: Math.max(17, 20 * menuFontScale), // Increased line height accordingly
    },
    menuItemActiveText: {
      fontSize: Math.max(15, 18 * menuFontScale), // Increased from 12,15 to 15,18
      fontWeight: '700',
      color: theme.colors.primary,
      letterSpacing: 0.3,
      lineHeight: Math.max(17, 20 * menuFontScale), // Increased line height accordingly
    },
    activeIndicator: {
      width: Math.max(3, 5 * menuItemScale),
      height: Math.max(3, 5 * menuItemScale),
      borderRadius: Math.max(1.5, 2.5 * menuItemScale),
      backgroundColor: theme.colors.primary,
    },
    menuItemDimmed: {
      opacity: 0.4,
      backgroundColor: theme.colors.surface + '20',
    },
    menuItemDimmedText: {
      color: theme.colors.textSecondary,
      opacity: 0.6,
    },
    footer: {
      height: footerSectionHeight,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
      paddingTop: Math.max(5, 8 * footerScale), // Further reduced top padding to bring theme much closer to dividing line
      justifyContent: 'space-around',
      marginTop: Math.max(-5, -2 * footerScale), // Negative margin to pull footer up closer to dividing line
      position: 'absolute', // Position absolutely to stick to bottom
      bottom: Math.max(15, 20 * footerScale), // Position from bottom of container
      left: Math.max(12, 16 * (isVerySmallScreen ? 0.8 : isSmallScreen ? 0.9 : 1.0)),
      right: Math.max(12, 16 * (isVerySmallScreen ? 0.8 : isSmallScreen ? 0.9 : 1.0)),
    },
    // Theme Selector Styles
    themeContainer: {
      marginBottom: Math.max(6, 10 * footerScale),
      position: 'relative',
    },
    themeButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Math.max(8, 12 * footerScale),
      paddingHorizontal: Math.max(8, 12 * footerScale),
      borderRadius: Math.max(6, 8 * footerScale),
      backgroundColor: theme.colors.surface + '80',
    },
    themeIconContainer: {
      marginRight: Math.max(6, 8 * footerScale),
      justifyContent: 'center',
      alignItems: 'center',
    },
    themeText: {
      fontSize: Math.max(12, 15 * footerFontScale),
      fontWeight: '600',
      color: theme.colors.textSecondary,
      letterSpacing: 0.3,
      lineHeight: Math.max(14, 17 * footerFontScale),
      flex: 1,
    },
    themeMenuOverlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1001,
    },
    themeMenuContainer: {
      width: '85%',
      maxWidth: containerWidth * 1.2,
      alignItems: 'center',
    },
    themeMenu: {
      width: '100%',
      backgroundColor: theme.colors.surface,
      borderRadius: Math.max(14, 16 * footerScale),
      borderWidth: 2,
      borderColor: theme.colors.primary,
      shadowColor: theme.colors.shadow,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.3,
      shadowRadius: 8,
      elevation: 12,
      paddingVertical: Math.max(18, 20 * footerScale),
      paddingHorizontal: Math.max(18, 20 * footerScale),
    },
    themeMenuHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Math.max(18, 20 * footerScale),
      paddingBottom: Math.max(10, 12 * footerScale),
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    themeMenuTitle: {
      fontSize: Math.max(19, 22 * footerFontScale),
      fontWeight: '700',
      color: theme.colors.text,
      letterSpacing: 0.5,
    },
    themeMenuClose: {
      width: Math.max(30, 32 * footerScale),
      height: Math.max(30, 32 * footerScale),
      borderRadius: Math.max(15, 16 * footerScale),
      backgroundColor: theme.colors.border,
      justifyContent: 'center',
      alignItems: 'center',
    },
    themeMenuCloseText: {
      fontSize: Math.max(19, 22 * footerFontScale),
      fontWeight: '600',
      color: theme.colors.textSecondary,
      lineHeight: Math.max(21, 24 * footerFontScale),
    },
    themeOptionsContainer: {
      gap: Math.max(14, 16 * footerScale),
    },
    themeOption: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Math.max(18, 20 * footerScale),
      paddingVertical: Math.max(16, 18 * footerScale),
      marginHorizontal: 0,
      marginVertical: Math.max(5, 6 * footerScale),
      borderRadius: Math.max(12, 12 * footerScale),
      borderWidth: 2,
      borderColor: 'transparent',
      minHeight: Math.max(65, 70 * footerScale),
    },
    themeOptionSelected: {
      borderColor: theme.colors.primary,
      borderWidth: 3,
      backgroundColor: theme.colors.primary + '15',
    },
    themePreview: {
      width: Math.max(36, 40 * footerScale),
      height: Math.max(36, 40 * footerScale),
      borderRadius: Math.max(18, 20 * footerScale),
      marginRight: Math.max(18, 20 * footerScale),
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 2,
      borderColor: theme.colors.border,
    },
    themePreviewDot: {
      width: Math.max(16, 18 * footerScale),
      height: Math.max(16, 18 * footerScale),
      borderRadius: Math.max(8, 9 * footerScale),
    },
    themeOptionText: {
      fontSize: Math.max(17, 20 * footerFontScale),
      fontWeight: '600',
      letterSpacing: 0.3,
      flex: 1,
    },
    themeSelectedIndicator: {
      width: Math.max(11, 12 * footerScale),
      height: Math.max(11, 12 * footerScale),
      borderRadius: Math.max(5.5, 6 * footerScale),
      marginLeft: Math.max(10, 12 * footerScale),
    },
    feedbackButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Math.max(8, 12 * footerScale),
      paddingHorizontal: Math.max(8, 12 * footerScale),
      borderRadius: Math.max(6, 8 * footerScale),
      backgroundColor: theme.colors.surface + '80',
      marginBottom: Math.max(6, 10 * footerScale),
    },
    feedbackIconContainer: {
      marginRight: Math.max(6, 8 * footerScale),
      justifyContent: 'center',
      alignItems: 'center',
    },
    feedbackText: {
      fontSize: Math.max(12, 15 * footerFontScale),
      fontWeight: '600',
      color: theme.colors.textSecondary,
      letterSpacing: 0.3,
      lineHeight: Math.max(14, 17 * footerFontScale),
    },
    logoutButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Math.max(8, 12 * footerScale),
      paddingHorizontal: Math.max(8, 12 * footerScale),
      borderRadius: Math.max(6, 8 * footerScale),
      backgroundColor: theme.colors.surface + '80',
    },
    logoutIcon: {
      fontSize: Math.max(14, 17 * footerFontScale),
      color: theme.colors.textSecondary,
      marginRight: Math.max(6, 8 * footerScale),
      transform: [{ rotate: '45deg' }],
      fontWeight: 'bold',
    },
    logoutText: {
      fontSize: Math.max(12, 15 * footerFontScale),
      fontWeight: '600',
      color: theme.colors.textSecondary,
      letterSpacing: 0.3,
      lineHeight: Math.max(14, 17 * footerFontScale),
    },
    goOnlineButton: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: Math.max(8, 12 * footerScale),
      paddingHorizontal: Math.max(8, 12 * footerScale),
      borderRadius: Math.max(6, 8 * footerScale),
      backgroundColor: theme.colors.primary + '20',
      marginBottom: Math.max(6, 8 * footerScale),
    },
    goOnlineIcon: {
      fontSize: Math.max(14, 17 * footerFontScale),
      marginRight: Math.max(6, 8 * footerScale),
    },
    goOnlineText: {
      fontSize: Math.max(12, 15 * footerFontScale),
      fontWeight: '600',
      color: theme.colors.primary,
      letterSpacing: 0.3,
      lineHeight: Math.max(14, 17 * footerFontScale),
    },
  });
};

export default Sidebar;