const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Ensure proper handling of SVG and other assets
config.resolver.assetExts.push('svg');

// Configure for development client that behaves like Expo Go
config.transformer = {
  ...config.transformer,
  // Disable all optimizations that break Expo Go behavior
  minifierConfig: {
    keep_fnames: true,
    mangle: false,
    compress: false,
  },
  // Preserve development-like behavior
  enableBabelRCLookup: false,
  enableBabelRuntime: false,
};

// Ensure modules are handled like in Expo Go
config.serializer = {
  ...config.serializer,
  // Don't optimize critical modules
  processModuleFilter: (module) => {
    // Always include gesture handler and webview modules as-is
    if (module.path.includes('react-native-gesture-handler') ||
        module.path.includes('react-native-webview')) {
      return true;
    }
    return true;
  },
};

module.exports = config;
