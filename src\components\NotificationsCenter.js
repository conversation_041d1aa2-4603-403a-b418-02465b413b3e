import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Animated,
  Easing
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';

// Format date to relative time
const formatDate = (dateStr) => {
  if (!dateStr) return '';

  try {
    // If dateStr is empty or just "Recent", return it as is
    if (!dateStr || dateStr === 'Recent') return 'Recent';

    // Try to parse the date
    const date = new Date(dateStr);

    // If date is invalid, return the original string
    if (isNaN(date.getTime())) {
      return dateStr;
    }

    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return '1d ago';
    if (diffDays < 30) return `${diffDays}d ago`;
    if (diffDays < 365) return `${Math.floor(diffDays / 30)}mo ago`;
    return `${Math.floor(diffDays / 365)}y ago`;
  } catch {
    return dateStr || 'Recent';
  }
};

// Animated Notification Card Component
const AnimatedNotificationCard = ({ notification, index, onPress, styles }) => {
  const slideAnim = useRef(new Animated.Value(50)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Staggered entrance animation
    const delay = index * 100; // 100ms delay between each card

    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        delay,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 600,
        delay,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        delay: delay + 200,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [index]);

  const handlePress = () => {
    // Add a small bounce effect on press
    Animated.sequence([
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        tension: 300,
        friction: 10,
        useNativeDriver: true,
      }),
    ]).start();

    onPress(notification);
  };

  return (
    <Animated.View
      style={{
        transform: [
          { translateY: slideAnim },
          { scale: scaleAnim }
        ],
        opacity: opacityAnim,
      }}
    >
      <TouchableOpacity
        style={styles.notificationCard}
        onPress={handlePress}
        activeOpacity={0.7}
      >
        {/* Bell Icon */}
        <View style={styles.iconContainer}>
          <Text style={styles.bellIcon}>🔔</Text>
        </View>

        {/* Content */}
        <View style={styles.contentContainer}>
          <Text style={styles.notificationTitle} numberOfLines={1}>
            {notification.title}
          </Text>
          <Text style={styles.courseInfo} numberOfLines={1}>
            {notification.courseCode}
          </Text>
          <Text style={styles.staffName} numberOfLines={1}>
            {notification.staff}
          </Text>
        </View>

        {/* Date */}
        <View style={styles.dateContainer}>
          <Text style={styles.dateText}>
            {formatDate(notification.date)}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

// Ghost loading component for notifications
const NotificationGhostCard = ({ theme }) => {
  const [shimmerAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    shimmerAnimation.start();
    return () => shimmerAnimation.stop();
  }, [shimmerAnim]);

  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const styles = createGhostStyles(theme);

  return (
    <View style={styles.ghostCard}>
      {/* Bell Icon Ghost */}
      <Animated.View style={[styles.ghostIcon, { opacity: shimmerOpacity }]} />

      {/* Content Ghost */}
      <View style={styles.ghostContent}>
        <Animated.View style={[styles.ghostTitle, { opacity: shimmerOpacity }]} />
        <Animated.View style={[styles.ghostCourse, { opacity: shimmerOpacity }]} />
        <Animated.View style={[styles.ghostStaff, { opacity: shimmerOpacity }]} />
      </View>

      {/* Date Ghost */}
      <View style={styles.ghostDateContainer}>
        <Animated.View style={[styles.ghostDate, { opacity: shimmerOpacity }]} />
      </View>
    </View>
  );
};

const NotificationsCenter = ({ notificationsWebView, notificationsData, onNotificationPress, theme, hasInitiallyLoaded, isRefreshing }) => {
  const [notifications, setNotifications] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [spinValue] = useState(new Animated.Value(0));

  // Handle notification press
  const handleNotificationPress = (notification) => {
    if (onNotificationPress) {
      onNotificationPress(notification);
    }
  };

  // Start spinning animation when loading
  useEffect(() => {
    if (isLoading) {
      const spinAnimation = Animated.loop(
        Animated.timing(spinValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        })
      );
      spinAnimation.start();
      return () => spinAnimation.stop();
    }
  }, [isLoading, spinValue]);





  // Update notifications when data is received (from cache or WebView)
  useEffect(() => {
    if (notificationsData !== null && notificationsData !== undefined) {
      if (Array.isArray(notificationsData)) {
        console.log('✅ NotificationsCenter: Setting notifications data:', notificationsData.length, 'items');
        setNotifications(notificationsData);
        // Mark as not loading since we have actual data
        setIsLoading(false);
      } else {
        console.log('📭 NotificationsCenter: Invalid notifications data format');
        setNotifications([]);
        setIsLoading(false);
      }
    } else {
      console.log('📭 NotificationsCenter: notificationsData is null - will show ghost loading');
      // Only show loading if we don't have cached notifications yet
      if (notifications.length === 0) {
        setIsLoading(true);
      }
    }
  }, [notificationsData]);

  // Simplified ghost loading logic - show only when no notifications and no data
  const shouldShowGhostLoading = notificationsData === null && notifications.length === 0;

  // Detailed debugging for ghost loading
  console.log('🔍 NotificationsCenter: Ghost loading debug:', {
    hasInitiallyLoaded,
    isRefreshing,
    notificationsData: notificationsData === null ? 'NULL' : `array[${notificationsData?.length}]`,
    shouldShowGhostLoading
  });

  // Update loading state - Show ghost loading only when we truly need it
  useEffect(() => {
    console.log('🔄 NotificationsCenter: Loading state check:', {
      hasInitiallyLoaded,
      notificationsDataExists: notificationsData !== null,
      notificationsCount: notificationsData?.length || 0,
      cachedNotificationsCount: notifications.length,
      isRefreshing,
      shouldShowGhostLoading
    });

    // Only show loading if we have no data at all (neither fresh nor cached)
    const shouldLoad = notificationsData === null && notifications.length === 0;
    console.log('🔄 NotificationsCenter: Setting loading to:', shouldLoad);
    setIsLoading(shouldLoad);
  }, [hasInitiallyLoaded, notificationsData, notifications.length, isRefreshing, shouldShowGhostLoading]);

  // Initialize with proper state management
  useEffect(() => {
    console.log('🚀 NotificationsCenter: Initializing with:', {
      hasInitiallyLoaded,
      hasNotificationsData: notificationsData !== null && notificationsData !== undefined,
      hasWebView: !!notificationsWebView
    });

    // Initialize notifications array
    if (notificationsData !== null && notificationsData !== undefined) {
      if (Array.isArray(notificationsData)) {
        setNotifications(notificationsData);
      } else {
        setNotifications([]);
      }
    } else {
      setNotifications([]);
    }

    // Set initial loading state based on hasInitiallyLoaded
    if (hasInitiallyLoaded !== undefined) {
      const shouldLoad = !hasInitiallyLoaded;
      console.log('🚀 NotificationsCenter: Initial loading state set to:', shouldLoad);
      setIsLoading(shouldLoad);
    } else {
      // Fallback logic if hasInitiallyLoaded is not provided
      console.log('⚠️ NotificationsCenter: hasInitiallyLoaded not provided, using fallback logic');
      if (notificationsData !== null && notificationsData !== undefined) {
        setIsLoading(false);
      } else if (!notificationsWebView) {
        setIsLoading(false);
      } else {
        setIsLoading(true);
      }
    }
  }, []);

  // Debug: Log when notifications change
  useEffect(() => {
    console.log('📋 NotificationsCenter: Notifications updated:', notifications.length, 'notifications');
  }, [notifications]);

  // Debug: Log when loading state changes
  useEffect(() => {
    console.log('🔄 NotificationsCenter: Loading state changed to:', isLoading);
  }, [isLoading]);

  // Create styles with theme
  const styles = createStyles(theme);

  // Debug logging for render
  console.log('🎨 NotificationsCenter: Rendering with shouldShowGhostLoading:', shouldShowGhostLoading);

  return (
    <View style={styles.container}>
      {/* Header with subtle divider */}
      <View style={styles.headerContainer}>
        <Text style={styles.header}>Notifications</Text>
        <View style={styles.headerDivider} />
      </View>

      {/* Content */}
      {shouldShowGhostLoading ? (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Show 10 ghost loading cards */}
          {Array.from({ length: 10 }, (_, index) => (
            <NotificationGhostCard key={`ghost-${index}`} theme={theme} />
          ))}
        </ScrollView>
      ) : (
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {notifications.length > 0 ? (
            notifications.map((notification, index) => (
              <AnimatedNotificationCard
                key={notification.id || `notification-${index}-${notification.title?.slice(0, 10) || ''}`}
                notification={notification}
                index={index}
                onPress={handleNotificationPress}
                styles={styles}
              />
            ))
          ) : (
            // Only show "No notifications found" if we have actual data (empty array)
            // If notificationsData is null, ghost loading should be showing instead
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No notifications found</Text>
            </View>
          )}
        </ScrollView>
      )}

    </View>
  );
};

const createStyles = (theme) => StyleSheet.create({
  container: {
    height : "110%",
    backgroundColor: theme.colors.surface,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 24,
    marginTop: -8, // Overlap slightly with the top section
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  headerContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  header: {
    fontSize: 20, // Decreased from 24
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 12,
  },
  headerDivider: {
    height: 2,
    backgroundColor: theme.colors.border,
    borderRadius: 1,
    marginHorizontal: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  loadingSpinner: {
    marginBottom: 16,
  },
  loadingSpinnerText: {
    fontSize: 32,
    color: '#EAB308', // Golden/yellow color to match theme
    fontWeight: 'bold',
  },
  loadingText: {
    fontSize: 14, // Decreased from 16
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontWeight: '500',
  },
  scrollView: {
    height: "80%",
    paddingHorizontal: 20,
  },
  scrollContent: {
    paddingBottom: 32,
    paddingTop: 8,
    flexGrow: 1,
  },
  notificationCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    marginHorizontal: 4,
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.bellBackground, // Use theme bell background color
    minHeight: 80,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.bellBackground, // Use theme bell background color
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  bellIcon: {
    fontSize: 20, // Decreased from 24
    color: theme.colors.bellText, // Use theme bell text color
  },
  contentContainer: {
    flex: 1,
    marginRight: 8,
  },
  notificationTitle: {
    fontSize: 14, // Decreased from 16
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  courseInfo: {
    fontSize: 12, // Decreased from 14
    color: theme.colors.primary,
    marginBottom: 2,
  },
  staffName: {
    fontSize: 10, // Decreased from 12
    color: theme.colors.textSecondary,
  },
  dateContainer: {
    alignItems: 'flex-end',
  },
  dateText: {
    fontSize: 10, // Decreased from 12
    color: theme.colors.textSecondary,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 14, // Decreased from 16
    color: theme.colors.textSecondary,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
});

// Ghost loading styles
const createGhostStyles = (theme) => StyleSheet.create({
  ghostCard: {
    backgroundColor: theme.colors.background,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    marginHorizontal: 4,
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.bellBackground,
    minHeight: 80,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  ghostIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: theme.colors.border,
    marginRight: 16,
  },
  ghostContent: {
    flex: 1,
    marginRight: 8,
  },
  ghostTitle: {
    height: 16,
    backgroundColor: theme.colors.border,
    borderRadius: 8,
    marginBottom: 8,
    width: '80%',
  },
  ghostCourse: {
    height: 14,
    backgroundColor: theme.colors.border,
    borderRadius: 7,
    marginBottom: 6,
    width: '60%',
  },
  ghostStaff: {
    height: 12,
    backgroundColor: theme.colors.border,
    borderRadius: 6,
    width: '40%',
  },
  ghostDateContainer: {
    alignItems: 'flex-end',
  },
  ghostDate: {
    height: 12,
    width: 40,
    backgroundColor: theme.colors.border,
    borderRadius: 6,
  },
});

export default NotificationsCenter;
