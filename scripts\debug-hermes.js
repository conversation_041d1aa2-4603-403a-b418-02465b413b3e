#!/usr/bin/env node

/**
 * Hermes Debugging Script for myGUC App
 * 
 * This script helps with debugging Hermes-based React Native apps
 * by providing utilities to:
 * - Extract and symbolicate crash logs
 * - Generate source maps
 * - Set up debugging environment
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class HermesDebugger {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.buildDir = path.join(this.projectRoot, 'android', 'app', 'build');
    this.sourceMapDir = path.join(this.buildDir, 'generated', 'sourcemaps', 'react');
  }

  log(message) {
    console.log(`🔍 [Hermes Debug] ${message}`);
  }

  error(message) {
    console.error(`❌ [Hermes Debug] ${message}`);
  }

  success(message) {
    console.log(`✅ [Hermes Debug] ${message}`);
  }

  /**
   * Check if <PERSON><PERSON> is enabled in the project
   */
  checkHermesEnabled() {
    this.log('Checking Her<PERSON> configuration...');

    try {
      // Try app.config.js first, then fallback to app.json
      let config;
      const configPath = path.join(this.projectRoot, 'app.config.js');

      if (fs.existsSync(configPath)) {
        // Import the config file
        delete require.cache[require.resolve(configPath)];
        const configModule = require(configPath);
        config = configModule.default || configModule;
      } else {
        // Fallback to app.json
        const appJsonPath = path.join(this.projectRoot, 'app.json');
        config = JSON.parse(fs.readFileSync(appJsonPath, 'utf8'));
      }

      const hermesEnabled = config.expo?.jsEngine === 'hermes' ||
                           config.expo?.android?.jsEngine === 'hermes' ||
                           config.expo?.android?.enableHermes === true;

      if (hermesEnabled) {
        this.success('Hermes is enabled in configuration');
      } else {
        this.error('Hermes is not enabled in configuration');
      }

      return hermesEnabled;
    } catch (error) {
      this.error(`Failed to check Hermes configuration: ${error.message}`);
      return false;
    }
  }

  /**
   * Generate source maps for debugging
   */
  generateSourceMaps() {
    this.log('Generating source maps...');
    
    try {
      // Create source maps directory if it doesn't exist
      if (!fs.existsSync(this.sourceMapDir)) {
        fs.mkdirSync(this.sourceMapDir, { recursive: true });
      }

      // Generate source maps using Metro
      const command = 'npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --sourcemap-output android/app/src/main/assets/index.android.bundle.map';
      
      this.log('Running Metro bundler to generate source maps...');
      execSync(command, { cwd: this.projectRoot, stdio: 'inherit' });
      
      this.success('Source maps generated successfully');
      return true;
    } catch (error) {
      this.error(`Failed to generate source maps: ${error.message}`);
      return false;
    }
  }

  /**
   * Symbolicate a Hermes crash stack trace
   */
  symbolicateStackTrace(stackTrace, sourceMapPath) {
    this.log('Symbolicating stack trace...');
    
    try {
      // Check if source map exists
      if (!fs.existsSync(sourceMapPath)) {
        this.error(`Source map not found at: ${sourceMapPath}`);
        return null;
      }

      // Use Metro's symbolicate command
      const tempFile = path.join(this.projectRoot, 'temp_stack_trace.txt');
      fs.writeFileSync(tempFile, stackTrace);
      
      const command = `npx metro-symbolicate ${sourceMapPath} < ${tempFile}`;
      const result = execSync(command, { cwd: this.projectRoot, encoding: 'utf8' });
      
      // Clean up temp file
      fs.unlinkSync(tempFile);
      
      this.success('Stack trace symbolicated successfully');
      return result;
    } catch (error) {
      this.error(`Failed to symbolicate stack trace: ${error.message}`);
      return null;
    }
  }

  /**
   * Extract crash logs from Android device
   */
  extractAndroidCrashLogs() {
    this.log('Extracting Android crash logs...');
    
    try {
      // Get crash logs using adb
      const command = 'adb logcat -d | grep -E "(FATAL|AndroidRuntime|myGUC|com.myguc.app)"';
      const logs = execSync(command, { encoding: 'utf8' });
      
      if (logs.trim()) {
        const logFile = path.join(this.projectRoot, 'crash_logs.txt');
        fs.writeFileSync(logFile, logs);
        this.success(`Crash logs saved to: ${logFile}`);
        return logs;
      } else {
        this.log('No crash logs found');
        return null;
      }
    } catch (error) {
      this.error(`Failed to extract crash logs: ${error.message}`);
      return null;
    }
  }

  /**
   * Setup debugging environment
   */
  setupDebugging() {
    this.log('Setting up debugging environment...');
    
    try {
      // Check Hermes configuration
      if (!this.checkHermesEnabled()) {
        return false;
      }

      // Generate source maps
      if (!this.generateSourceMaps()) {
        return false;
      }

      // Create debugging info file
      const debugInfo = {
        timestamp: new Date().toISOString(),
        hermesEnabled: true,
        sourceMapPath: path.join(this.projectRoot, 'android/app/src/main/assets/index.android.bundle.map'),
        bundlePath: path.join(this.projectRoot, 'android/app/src/main/assets/index.android.bundle'),
        instructions: [
          '1. Build your app with: npx eas build --profile debug --platform android',
          '2. Install the APK on your device',
          '3. If the app crashes, run: node scripts/debug-hermes.js --extract-logs',
          '4. To symbolicate a stack trace, run: node scripts/debug-hermes.js --symbolicate <stack_trace_file>'
        ]
      };

      const debugInfoFile = path.join(this.projectRoot, 'debug_info.json');
      fs.writeFileSync(debugInfoFile, JSON.stringify(debugInfo, null, 2));
      
      this.success('Debugging environment setup complete');
      this.log(`Debug info saved to: ${debugInfoFile}`);
      
      return true;
    } catch (error) {
      this.error(`Failed to setup debugging environment: ${error.message}`);
      return false;
    }
  }

  /**
   * Print debugging instructions
   */
  printInstructions() {
    console.log(`
🔍 Hermes Debugging Instructions for myGUC App
===============================================

1. Build Debug APK:
   npx eas build --profile debug --platform android --clear-cache

2. Install and Test:
   - Install the APK on your device
   - Reproduce the crash

3. Extract Crash Logs:
   node scripts/debug-hermes.js --extract-logs

4. Symbolicate Stack Trace:
   node scripts/debug-hermes.js --symbolicate <stack_trace_file>

5. Debug with Chrome DevTools:
   - Enable USB debugging on your device
   - Run: adb reverse tcp:8081 tcp:8081
   - Open Chrome and go to: chrome://inspect
   - Look for your app in the Remote Target list

6. View App Logs:
   adb logcat | grep -E "(myGUC|com.myguc.app|ReactNativeJS)"

📱 Useful ADB Commands:
- adb devices (list connected devices)
- adb logcat -c (clear logs)
- adb logcat -d > logs.txt (save logs to file)
- adb shell am force-stop com.myguc.app (force stop app)

🔧 Troubleshooting:
- Make sure Hermes is enabled in app.json
- Ensure source maps are generated
- Check that the APK is built with debugging enabled
- Verify USB debugging is enabled on your device
`);
  }
}

// CLI Interface
function main() {
  const debugger = new HermesDebugger();
  const args = process.argv.slice(2);

  if (args.length === 0) {
    debugger.printInstructions();
    return;
  }

  const command = args[0];

  switch (command) {
    case '--setup':
      debugger.setupDebugging();
      break;
    
    case '--extract-logs':
      debugger.extractAndroidCrashLogs();
      break;
    
    case '--symbolicate':
      if (args.length < 2) {
        debugger.error('Please provide a stack trace file path');
        return;
      }
      const stackTraceFile = args[1];
      const sourceMapPath = path.join(debugger.projectRoot, 'android/app/src/main/assets/index.android.bundle.map');
      
      if (fs.existsSync(stackTraceFile)) {
        const stackTrace = fs.readFileSync(stackTraceFile, 'utf8');
        const symbolicated = debugger.symbolicateStackTrace(stackTrace, sourceMapPath);
        if (symbolicated) {
          console.log('\n📋 Symbolicated Stack Trace:\n');
          console.log(symbolicated);
        }
      } else {
        debugger.error(`Stack trace file not found: ${stackTraceFile}`);
      }
      break;
    
    case '--check':
      debugger.checkHermesEnabled();
      break;
    
    case '--help':
    default:
      debugger.printInstructions();
      break;
  }
}

if (require.main === module) {
  main();
}

module.exports = HermesDebugger;
