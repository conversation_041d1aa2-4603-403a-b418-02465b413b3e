import React from 'react';
import { WebView } from 'react-native-webview';

/**
 * WebView utility functions for ensuring fresh, clean WebView instances
 */

/**
 * Clears WebView cookies and cache before creating a new instance
 * Note: In react-native-webview, clearing is handled by using incognito mode and cacheEnabled: false
 * @returns {Promise<void>}
 */
export const clearWebViewSession = async () => {
  try {
    console.log('🧹 Preparing fresh WebView session...');
    // Note: Modern react-native-webview doesn't have static clearCache/clearCookies methods
    // Instead, we rely on incognito mode and cacheEnabled: false in WebView props
    // This ensures each WebView instance starts with a clean session
    console.log('✅ WebView session preparation complete');
  } catch (error) {
    console.log('⚠️ WebView session preparation failed:', error);
    // Continue anyway - this shouldn't block execution
  }
};

/**
 * Creates a fresh WebView component with cleared session
 * @param {Object} props - WebView props
 * @param {string} uniqueKey - Unique key for the WebView instance
 * @returns {Promise<JSX.Element>} Fresh WebView component
 */
export const createFreshWebView = async (props, uniqueKey = null) => {
  // Clear session before creating new WebView
  await clearWebViewSession();
  
  // Generate unique key if not provided
  const key = uniqueKey || `fresh-webview-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  console.log(`🆕 Creating fresh WebView with key: ${key}`);
  
  return (
    <WebView
      key={key}
      {...props}
      // Force fresh session properties
      javaScriptEnabled={true}
      domStorageEnabled={true}
      startInLoadingState={false}
      // Ensure no cache reuse
      cacheEnabled={false}
      incognito={true}
    />
  );
};

/**
 * Creates a fresh WebView for authentication with embedded credentials
 * @param {string} username - Username for authentication
 * @param {string} password - Password for authentication
 * @param {string} baseUrl - Base URL without credentials
 * @param {Object} additionalProps - Additional WebView props
 * @param {string} purpose - Purpose identifier for logging
 * @returns {Promise<JSX.Element>} Fresh authenticated WebView
 */
export const createFreshAuthWebView = async (username, password, baseUrl, additionalProps = {}, purpose = 'auth') => {
  // Clear session first
  await clearWebViewSession();
  
  // Create URL with embedded credentials - properly encode username and password
  const encodedUsername = encodeURIComponent(username);
  const encodedPassword = encodeURIComponent(password);
  const urlWithCredentials = baseUrl.replace('https://', `https://${encodedUsername}:${encodedPassword}@`);
  
  const uniqueKey = `${purpose}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  console.log(`🔐 Creating fresh authenticated WebView for ${purpose} with key: ${uniqueKey}`);
  
  return (
    <WebView
      key={uniqueKey}
      source={{ uri: urlWithCredentials }}
      javaScriptEnabled={true}
      domStorageEnabled={true}
      startInLoadingState={false}
      cacheEnabled={false}
      incognito={true}
      mixedContentMode="compatibility"
      {...additionalProps}
    />
  );
};

/**
 * Disposes of a WebView reference and clears its session
 * @param {Object} webViewRef - React ref to the WebView
 * @param {string} purpose - Purpose identifier for logging
 */
export const disposeWebView = async (webViewRef, purpose = 'unknown') => {
  try {
    console.log(`🗑️ Disposing WebView for ${purpose}...`);
    
    if (webViewRef && webViewRef.current) {
      // Clear the reference
      webViewRef.current = null;
    }
    
    // Clear session after disposal
    await clearWebViewSession();
    
    console.log(`✅ WebView disposed for ${purpose}`);
  } catch (error) {
    console.log(`⚠️ Error disposing WebView for ${purpose}:`, error);
  }
};

/**
 * Force refresh a WebView by clearing session and reloading
 * @param {Object} webViewRef - React ref to the WebView
 * @param {string} purpose - Purpose identifier for logging
 */
export const refreshWebView = async (webViewRef, purpose = 'unknown') => {
  try {
    console.log(`🔄 Refreshing WebView for ${purpose}...`);
    
    // Clear session first
    await clearWebViewSession();
    
    // Reload the WebView if reference exists
    if (webViewRef && webViewRef.current) {
      webViewRef.current.reload();
    }
    
    console.log(`✅ WebView refreshed for ${purpose}`);
  } catch (error) {
    console.log(`⚠️ Error refreshing WebView for ${purpose}:`, error);
  }
};
