import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  PanResponder,
  Modal,
  Animated,
} from 'react-native';

import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import BackIcon from '../../components/BackIcon';
import { clearWebViewSession, disposeWebView } from '../../utils/WebViewUtils';
import { checkDemoMode } from '../../utils/DemoData';
import { useTheme } from '../../contexts/ThemeContext';
import { useOffline } from '../../contexts/OfflineContext';
import { showOfflineUnavailablePopup } from '../../utils/OfflineUtils';
import { getResponsiveHeaderLayout, getContentPaddingForHeader } from '../../utils/HeaderUtils';
import Svg, { Path, Circle, X } from 'react-native-svg';

// Demo Mode Overlay Component
const DemoModeOverlay = ({ visible, onClose, theme }) => {
  const [overlayAnim] = useState(new Animated.Value(0));
  const [scaleAnim] = useState(new Animated.Value(0.8));

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(overlayAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        })
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(overlayAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        })
      ]).start();
    }
  }, [visible]);

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <Animated.View
        style={[
          demoOverlayStyles.overlay,
          {
            opacity: overlayAnim,
          }
        ]}
      >
        <TouchableOpacity 
          style={demoOverlayStyles.overlayTouchable}
          activeOpacity={1}
          onPress={onClose}
        >
          <Animated.View
            style={[
              demoOverlayStyles.modal,
              {
                backgroundColor: theme.colors.surface,
                borderColor: theme.colors.primary,
                transform: [{ scale: scaleAnim }]
              }
            ]}
          >
            <TouchableOpacity
              style={demoOverlayStyles.modalContent}
              activeOpacity={1}
              onPress={(e) => e.stopPropagation()}
            >
              {/* Header with close button */}
              <View style={demoOverlayStyles.header}>
                <View style={demoOverlayStyles.iconContainer}>
                  <Svg width={32} height={32} viewBox="0 0 24 24" fill={theme.colors.primary}>
                    <Path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                  </Svg>
                </View>
                <TouchableOpacity
                  style={demoOverlayStyles.closeButton}
                  onPress={onClose}
                >
                  <Text style={[demoOverlayStyles.closeButtonText, { color: theme.colors.textSecondary }]}>×</Text>
                </TouchableOpacity>
              </View>

              {/* Content */}
              <View style={demoOverlayStyles.content}>
                <Text style={[demoOverlayStyles.title, { color: theme.colors.text }]}>
                  Demo Mode - Mail Access
                </Text>
                
                <Text style={[demoOverlayStyles.description, { color: theme.colors.textSecondary }]}>
                  This is a demo account, so you can't log into the mail system as it requires real GUC credentials for authentication.
                </Text>
                
                <Text style={[demoOverlayStyles.description, { color: theme.colors.textSecondary }]}>
                  However, this screen shows you how the mail interface works - it's a WebView that normally connects to:
                </Text>
                
                <View style={[demoOverlayStyles.urlContainer, { backgroundColor: theme.colors.background }]}>
                  <Text style={[demoOverlayStyles.urlText, { color: theme.colors.primary }]}>
                    mail.guc.edu.eg
                  </Text>
                </View>
                
                <Text style={[demoOverlayStyles.description, { color: theme.colors.textSecondary }]}>
                  In the real app with actual credentials, you would be automatically logged in and able to access your GUC email directly.
                </Text>
              </View>

              {/* Footer */}
              <View style={demoOverlayStyles.footer}>
                <TouchableOpacity
                  style={[demoOverlayStyles.button, { backgroundColor: theme.colors.primary }]}
                  onPress={onClose}
                >
                  <Text style={[demoOverlayStyles.buttonText, { color: theme.colors.primaryText }]}>
                    Got it!
                  </Text>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>
    </Modal>
  );
};

// Demo overlay styles
const demoOverlayStyles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  overlayTouchable: {
    flex: 1,
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modal: {
    width: '100%',
    maxWidth: 400,
    borderRadius: 16,
    borderWidth: 2,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  modalContent: {
    padding: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    paddingBottom: 10,
  },
  iconContainer: {
    flex: 1,
    alignItems: 'flex-start',
  },
  closeButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  content: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    lineHeight: 24,
    textAlign: 'left',
    marginBottom: 12,
  },
  urlContainer: {
    padding: 12,
    borderRadius: 8,
    marginVertical: 8,
    alignItems: 'center',
  },
  urlText: {
    fontSize: 16,
    fontWeight: '600',
    fontFamily: 'monospace',
  },
  footer: {
    padding: 20,
    paddingTop: 10,
  },
  button: {
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

const MailScreen = ({ navigation }) => {
  const { theme, currentThemeName } = useTheme();
  const { isOfflineMode } = useOffline();
  const safeCurrentThemeName = currentThemeName || 'dark';



  const [credentials, setCredentials] = useState(null);
  const [mailUrl, setMailUrl] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const [isDemoMode, setIsDemoMode] = useState(false);
  const [showDemoOverlay, setShowDemoOverlay] = useState(false);
  const webViewRef = useRef(null);
  const injectionSuccessful = useRef(false);
  const storageCheckDone = useRef(false);
  const storageCheckCount = useRef(0);
  const storageButtonClicked = useRef(false);

  // Refs for tracking background operations and component mount state
  const isMountedRef = useRef(true);
  const activeTimeoutsRef = useRef(new Set());

  // Comprehensive function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 MailScreen: Killing all background operations...');

    // Mark component as unmounted to prevent state updates
    isMountedRef.current = false;

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();

    // Reset injection flags
    injectionSuccessful.current = false;
    storageButtonClicked.current = false;
    storageCheckDone.current = false;
    storageCheckCount.current = 0;

    // Dispose of WebView
    await disposeWebView(webViewRef, 'mail-webview');

    console.log('✅ MailScreen: All background operations killed');
  };

  // Safe timeout wrapper that tracks timeouts for cleanup
  const safeSetTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      activeTimeoutsRef.current.delete(timeoutId);
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  // Handle screen focus/unfocus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 MailScreen: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;
      setIsFocused(true);
      loadCredentials();

      return () => {
        console.log('🔄 MailScreen: Screen losing focus - killing background operations...');
        setIsFocused(false);
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Kill background operations when navigating away completely
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 MailScreen: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  const loadCredentials = async () => {
    try {
      console.log('🔑 Loading credentials for Mail...');

      // Check if we're in demo mode first
      const demoMode = await checkDemoMode();
      setIsDemoMode(demoMode);

      if (demoMode) {
        console.log('🎭 Demo mode detected - showing mail without auto-login');
        // Set mail URL without credentials for demo mode
        setMailUrl('https://mail.guc.edu.eg/');
        
        // Show demo overlay after a brief delay to let WebView load
        setTimeout(() => {
          setShowDemoOverlay(true);
        }, 500); // Reduced from 1500ms to 500ms
        
        return;
      }

      // Clear WebView session before setting up mail
      await clearWebViewSession();

      // Get stored credentials
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        return;
      }

      console.log('✅ Credentials found, setting up mail URL...');

      // Store credentials for use
      setCredentials({ username: storedUsername, password: storedPassword });

      // Set mail URL
      setMailUrl('https://mail.guc.edu.eg/');

      console.log('🌐 Mail URL set, WebView will load...');
    } catch (error) {
      console.error('❌ Error loading credentials:', error);
    }
  };

  const handleBack = () => {
    console.log('📧 Mail: Back button pressed, navigating to Portal');
    navigation.navigate('Portal');
  };

  // Swipe gesture handler for back navigation
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Same logic for capture
      const { dx, dy } = gestureState;
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted
    },
    onPanResponderMove: () => {
      // Handle move if needed
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      // Only handle horizontal swipes with sufficient distance
      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - navigate back to Portal
          console.log('🔄 Mail: Swipe right detected, navigating to Portal');
          navigation.navigate('Portal');
        }
        // Ignore swipe left since we don't have sidebar
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination once we have it
    onShouldBlockNativeResponder: () => true, // Block native responders only when we have the gesture
  });

  const injectCredentialsAndSetupPopupRemoval = () => {
    if (webViewRef.current && credentials && !injectionSuccessful.current) {
      console.log('💉 Injecting credentials and setting up fast popup removal...');

      const injectionScript = `
        try {
          // Function to inject credentials
          function injectCredentials() {
            let usernameInjected = false;
            let passwordInjected = false;

            // Find username input
            const usernameInput = document.querySelector('input#username[name="username"].signInInputText');
            if (usernameInput) {
              usernameInput.value = '${credentials.username}';
              usernameInput.dispatchEvent(new Event('input', { bubbles: true }));
              usernameInput.dispatchEvent(new Event('change', { bubbles: true }));
              console.log('✅ Username injected');
              usernameInjected = true;
            }

            // Find password input
            const passwordInput = document.querySelector('input[name="password"].signInInputText') ||
                                 document.querySelector('input[type="password"].signInInputText');
            if (passwordInput) {
              passwordInput.value = '${credentials.password}';
              passwordInput.dispatchEvent(new Event('input', { bubbles: true }));
              passwordInput.dispatchEvent(new Event('change', { bubbles: true }));
              console.log('✅ Password injected');
              passwordInjected = true;
            }

            // If both credentials injected, click sign in
            if (usernameInjected && passwordInjected) {
              const signInButton = document.querySelector('.signinbutton');
              if (signInButton) {
                signInButton.click();
                console.log('✅ Sign in button clicked');

                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'INJECTION_SUCCESS',
                  message: 'Credentials injected and sign in clicked'
                }));
                return true;
              }
            }
            return false;
          }

          // Function to remove storage popup instantly
          function removeStoragePopup() {
            const storageSelectors = [
              'div[autoid="_fce_a"].ms-bgc-w',
              '.ms-Modal',
              '[role="dialog"]',
              '.ms-Dialog',
              '.storage-exceeded',
              '.ms-MessageBar'
            ];

            let removed = false;
            for (const selector of storageSelectors) {
              const elements = document.querySelectorAll(selector);
              elements.forEach(element => {
                if (element && (element.textContent.toLowerCase().includes('storage') ||
                               element.textContent.toLowerCase().includes('quota') ||
                               element.textContent.toLowerCase().includes('limit'))) {
                  element.remove();
                  removed = true;
                  console.log('🗑️ Storage popup removed instantly');
                }
              });
            }

            // Remove modal backgrounds
            const backgrounds = document.querySelectorAll('.modalBackground, .ms-Overlay, .ms-Modal-scrollableContent');
            backgrounds.forEach(bg => {
              bg.remove();
              removed = true;
            });

            if (removed) {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'STORAGE_POPUP_REMOVED',
                message: 'Storage popup detected and removed instantly'
              }));
            }

            return removed;
          }

          // Try to inject credentials immediately if on login page
          if (window.location.href.includes('logon.aspx')) {
            injectCredentials();
          }

          // Immediate popup removal on page load (since div is always there in background)
          removeStoragePopup();

          // Set up fast interval checking for popup removal (old approach but faster)
          let popupCheckCount = 0;
          const maxChecks = 20; // Check for 10 seconds total

          const popupInterval = setInterval(() => {
            removeStoragePopup();
            popupCheckCount++;

            if (popupCheckCount >= maxChecks) {
              clearInterval(popupInterval);
              console.log('✅ Popup removal checks completed');
            }
          }, 500); // Check every 500ms for faster response

          console.log('✅ Fast popup removal system active');

          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'POPUP_REMOVAL_SETUP',
            message: 'Fast popup removal system active'
          }));

        } catch (error) {
          console.log('❌ Error in setup:', error);
          window.ReactNativeWebView.postMessage(JSON.stringify({
            type: 'INJECTION_ERROR',
            message: error.toString()
          }));
        }
      `;

      webViewRef.current.injectJavaScript(injectionScript);
    }
  };



  const onNavigationStateChange = (navState) => {
    // Only process navigation changes when screen is focused
    if (!isFocused) {
      return;
    }

    console.log('📧 Mail navigation state:', navState.url);

    // Single injection with fast popup removal setup - works for all pages
    if (!navState.loading && navState.url.includes('mail.guc.edu.eg')) {
      // Single injection that handles both login and popup detection
      safeSetTimeout(() => {
        injectCredentialsAndSetupPopupRemoval();
      }, 300); // Single optimized delay
    }
  };

  const onMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);

      if (data.type === 'INJECTION_SUCCESS') {
        injectionSuccessful.current = true;
        console.log('🎉 Credentials successfully injected and form submitted!');
      } else if (data.type === 'INJECTION_ERROR') {
        console.log('❌ Injection error:', data.message);
      } else if (data.type === 'POPUP_REMOVAL_SETUP') {
        console.log('✅ Fast popup removal system set up successfully');
      } else if (data.type === 'STORAGE_POPUP_REMOVED') {
        console.log('🗑️ Storage popup detected and removed by fast removal system');
      } else if (data.type === 'STORAGE_CHECK') {
        // Legacy handler - kept for compatibility
        console.log(`📦 Storage check: ${data.exists ? 'Found and removed' : 'Not found'}`);
      }
    } catch (error) {
      console.log('❌ Error parsing WebView message:', error);
    }
  };

  // Create dynamic header layout
  const headerLayout = getResponsiveHeaderLayout('Mail', theme, safeCurrentThemeName);
  
  // Override title color for Mail screen to ensure it's always white
  const mailHeaderLayout = {
    ...headerLayout,
    styles: {
      ...headerLayout.styles,
      titleText: {
        ...headerLayout.styles.titleText,
        color: '#FFFFFF', // Force white for Mail screen
      },
      buttonContainer: {
        ...headerLayout.styles.buttonContainer,
        backgroundColor: 'rgba(255, 255, 255, 0.3)', // Semi-transparent white for buttons (like before)
        borderColor: 'rgba(255, 255, 255, 0.5)', // White border
        borderWidth: 1,
      }
    }
  };

  // Create styles with dynamic header layout
  const styles = createStyles(theme, safeCurrentThemeName, mailHeaderLayout);

  return (
    <View style={styles.container} {...swipeGestureHandler.panHandlers}>
      {/* Blue Header Background Area */}
      <View style={styles.headerBackground} />
      
      {/* Back Button */}
      <View style={[mailHeaderLayout.leftButtonContainer, { top: 50 }]}>
        <TouchableOpacity
          style={[
            mailHeaderLayout.styles.buttonContainer,
            styles.backButtonSpecific
          ]}
          onPress={() => {
            console.log('🔥 BUTTON CLICKED! Touch event received');
            handleBack();
          }}
          onPressIn={() => console.log('🔥 BUTTON PRESS IN - Touch started')}
          onPressOut={() => console.log('🔥 BUTTON PRESS OUT - Touch ended')}
          activeOpacity={0.7}
          hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
        >
          <BackIcon size={20} color="#ffffff" strokeWidth={3} />
        </TouchableOpacity>
      </View>

      {/* Mail Title */}
      <View style={[mailHeaderLayout.styles.titleContainer, { top: 58 }]}>
        <Text style={[mailHeaderLayout.styles.titleText, styles.titleSpecific]}>Mail</Text>
      </View>

      {/* WebView Container with white background */}
      <View style={[styles.webViewContainer, getContentPaddingForHeader(mailHeaderLayout).contentContainer]}>
        {mailUrl && isFocused ? (
          <>
            <WebView
              ref={webViewRef}
              source={{ uri: mailUrl }}
              style={styles.webView}
              javaScriptEnabled={true}
              domStorageEnabled={true}
              startInLoadingState={true}
              cacheEnabled={false}
              incognito={true}
              mixedContentMode="compatibility"
              onNavigationStateChange={isDemoMode ? undefined : onNavigationStateChange}
              onMessage={isDemoMode ? undefined : onMessage}
              key={`mail-webview-${Date.now()}`}
            />

            {/* Demo Mode Overlay Modal */}
            <DemoModeOverlay 
              visible={showDemoOverlay}
              onClose={() => setShowDemoOverlay(false)}
              theme={theme}
            />
          </>
        ) : isFocused ? (
          null // Removed loading text as requested
        ) : null}
      </View>
    </View>
  );
};

// Create styles function that uses theme and header layout
const createStyles = (theme, safeCurrentThemeName, headerLayout) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0078d4', // Outlook blue background for entire screen
  },
  headerBackground: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 100, // Height of the header area
    backgroundColor: '#0078d4', // Outlook blue background for header area
    zIndex: 500, // Below buttons but above content
  },
  backButtonSpecific: {
    // Semi-transparent white circular background as it was originally
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.5)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  titleSpecific: {
    color: '#FFFFFF', // Force white text for Mail title, override any theme color
    fontWeight: 'bold',
    fontSize: 30, // Ensure consistent size
  },
  webViewContainer: {
    flex: 1,
    backgroundColor: '#ffffff', // White background for WebView content area only
    paddingBottom: 20, // White margin at bottom
  },
  webView: {
    flex: 1,
    backgroundColor: '#ffffff', // White background like Outlook
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 0,
    backgroundColor: '#ffffff', // White background like Outlook
  },
  loadingText: {
    fontSize: 18,
    color: '#0078d4', // Outlook blue text
    textAlign: 'center',
  },
});

export default MailScreen;
