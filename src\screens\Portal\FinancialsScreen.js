import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  ActivityIndicator,
  Alert,
  Platform,
  PanResponder,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import Sidebar from '../../components/Sidebar';
import HamburgerIcon from '../../components/HamburgerIcon';
import RefreshIcon from '../../components/RefreshIcon';
import { clearWebViewSession, disposeWebView } from '../../utils/WebViewUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { handleDemoModeInit, handleDemoModeRefresh, getDemoFinancialsData } from '../../utils/DemoModeHelper';

const FinancialsScreen = ({ navigation }) => {
  // Theme context with defensive programming
  let theme, currentThemeName;
  try {
    const themeContext = useTheme();
    theme = themeContext.theme;
    currentThemeName = themeContext.currentThemeName;
  } catch (error) {
    console.log('Theme context error:', error);
    // Fallback theme - Updated to match new dark theme
    theme = {
      colors: {
        background: '#2C2C2C', // Updated to new dark grey
        surface: '#3A3A3A', // Updated to new surface grey
        text: '#FFFFFF',
        textSecondary: '#B0B0B0', // Lightened
        primary: '#EAB308',
        primaryText: '#2C2C2C', // Updated to match new background
        border: '#555555', // Lightened
        shadow: '#1A1A1A' // Lightened
      }
    };
    currentThemeName = 'dark';
  }

  // Fallback for currentThemeName to prevent undefined errors
  const safeCurrentThemeName = currentThemeName || 'dark';

  const [isLoading, setIsLoading] = useState(true);
  const [credentials, setCredentials] = useState(null);
  const [financialsUrl, setFinancialsUrl] = useState('');
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [sidebarAnim] = useState(new Animated.Value(-300)); // Start off-screen
  const [financialsData, setFinancialsData] = useState([]);
  const [isLoadingFinancials, setIsLoadingFinancials] = useState(false);
  const [refreshRotation] = useState(new Animated.Value(0)); // For rotating refresh arrow
  const [isUpdatingData, setIsUpdatingData] = useState(false);
  const [hasCachedData, setHasCachedData] = useState(false);
  const [hasDataBeenFetched, setHasDataBeenFetched] = useState(false);
  const webViewRef = useRef(null);

  // Refs for tracking background operations and component mount state
  const isMountedRef = useRef(true);
  const activeTimeoutsRef = useRef(new Set());

  // Comprehensive function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 FinancialsScreen: Killing all background operations...');

    // Mark component as unmounted to prevent state updates
    isMountedRef.current = false;

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();

    // Stop all animations
    refreshRotation.stopAnimation();
    sidebarAnim.stopAnimation();

    // Reset all loading states to prevent cache corruption
    setIsLoading(false);
    setIsLoadingFinancials(false);
    setIsUpdatingData(false);

    // Dispose of WebView
    await disposeWebView(webViewRef, 'financials-webview');

    console.log('✅ FinancialsScreen: All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ FinancialsScreen: Prevented ${stateName} state update after unmount`);
    }
  };

  // Safe timeout wrapper that tracks timeouts for cleanup
  const safeSetTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      activeTimeoutsRef.current.delete(timeoutId);
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  useEffect(() => {
    initializeScreenWithCache();

    // Cleanup function
    return () => {
      console.log('🧹 FinancialsScreen: Component unmounting - cleaning up...');
      killAllBackgroundOperations();
    };
  }, []);

  // Handle navigation focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 FinancialsScreen: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 FinancialsScreen: Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Kill background operations when navigating away completely
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 FinancialsScreen: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  // Initialize screen with cache-first strategy
  const initializeScreenWithCache = async () => {
    console.log('🚀 Initializing FinancialsScreen with cache-first strategy');

    // Check for demo mode first
    const demoHandled = await handleDemoModeInit(
      setIsLoading,
      setFinancialsData,
      getDemoFinancialsData()
    );

    if (demoHandled) {
      setHasDataBeenFetched(true);
      // Set demo URL to satisfy rendering condition
      setFinancialsUrl('demo://financials');
      console.log('🎭 Demo financials data loaded successfully');
      return;
    }

    // Try to load cached data first
    const cacheLoaded = await loadFromCache();

    if (cacheLoaded) {
      console.log('✅ Cached financials data loaded, starting background refresh');
      // Show update indicator and fetch fresh data in background
      setIsUpdatingData(true);
      startRotationAnimation(); // Start rotation for background update
      await loadCredentialsAndSetupFinancials(true); // Pass true to indicate background refresh
    } else {
      console.log('📭 No cache found, loading fresh financials data');
      // No cache, load normally
      await loadCredentialsAndSetupFinancials(false);
    }
  };

  // Cache management functions
  const CACHE_KEY = 'financials_cache';
  const CACHE_TIMESTAMP_KEY = 'financials_cache_timestamp';

  const saveToCache = async (data) => {
    try {
      const cacheData = {
        financialsData: data,
        timestamp: Date.now()
      };
      await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
      await AsyncStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString());
      console.log('📦 Financials data cached successfully');
    } catch (error) {
      console.log('❌ Error saving financials to cache:', error);
    }
  };

  const loadFromCache = async () => {
    try {
      const cachedData = await AsyncStorage.getItem(CACHE_KEY);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        console.log('📦 Loading cached financials data');

        // Set cached data to state
        setFinancialsData(parsedData.financialsData || []);
        setHasCachedData(true);
        setHasDataBeenFetched(true); // Mark that we have data (from cache)
        setIsLoading(false);

        return true; // Cache loaded successfully
      }
      return false; // No cache found
    } catch (error) {
      console.log('❌ Error loading financials from cache:', error);
      return false;
    }
  };

  const clearCache = async () => {
    try {
      await AsyncStorage.removeItem(CACHE_KEY);
      await AsyncStorage.removeItem(CACHE_TIMESTAMP_KEY);
      console.log('🗑️ Financials cache cleared');
    } catch (error) {
      console.log('❌ Error clearing financials cache:', error);
    }
  };

  // Format date from "Sunday,22-06-2025" to "Sun, 22 Jun 2025"
  const formatDate = (dateString) => {
    try {
      if (!dateString) return dateString;

      // Handle format like "Sunday,22-06-2025"
      const parts = dateString.split(',');
      if (parts.length === 2) {
        const datePart = parts[1].trim();

        // Parse the date part "22-06-2025"
        const [day, month, year] = datePart.split('-');

        // Create a proper date object
        const date = new Date(year, month - 1, day);

        // Format to a more readable format
        const options = {
          weekday: 'short',
          day: 'numeric',
          month: 'short',
          year: 'numeric'
        };

        return date.toLocaleDateString('en-US', options);
      }

      return dateString; // Return original if parsing fails
    } catch (error) {
      console.log('❌ Error formatting date:', error);
      return dateString; // Return original if error
    }
  };

  const openSidebar = () => {
    setIsSidebarVisible(true);
    Animated.timing(sidebarAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSidebar = () => {
    Animated.timing(sidebarAnim, {
      toValue: -300,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsSidebarVisible(false);
    });
  };

  // Swipe gesture handler for opening/closing sidebar - Enhanced for Android
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Only capture significant horizontal swipes to prevent Android system gestures
      const { dx, dy } = gestureState;
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted - prevent other handlers
      return true;
    },
    onPanResponderMove: () => {
      // Optional: Add visual feedback during swipe
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - open sidebar
          openSidebar();
        } else {
          // Swipe left - close sidebar if it's open
          if (isSidebarVisible) {
            closeSidebar();
          }
        }
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination
    onShouldBlockNativeResponder: () => true, // Block native responders
  });

  // Rotation animation functions
  const startRotationAnimation = () => {
    refreshRotation.setValue(0);
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopRotationAnimation = () => {
    Animated.timing(refreshRotation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const loadCredentialsAndSetupFinancials = async (isBackgroundRefresh = false) => {
    try {
      console.log('🔄 Loading credentials for financials...');

      // Check for demo mode
      const demoHandled = await handleDemoModeInit(
        setIsLoadingFinancials,
        setFinancialsData,
        getDemoFinancialsData()
      );

      if (demoHandled) {
        setIsLoading(false);
        setHasDataBeenFetched(true);

        // Set a placeholder URL for demo mode to satisfy rendering condition
        setFinancialsUrl('demo://financials');

        return;
      }

      // Clear WebView session before setting up financials (only if not background refresh)
      if (!isBackgroundRefresh) {
        await clearWebViewSession();
      }

      // Get stored credentials from localStorage
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        if (!isBackgroundRefresh) {
          // Alert.alert(
          //   'Authentication Required',
          //   'Please login first to access financials.',
          //   [
          //     {
          //       text: 'Go to Login',
          //       onPress: () => navigation.navigate('Login')
          //     }
          //   ]
          // );
        }
        setIsUpdatingData(false);
        return;
      }

      console.log('✅ Credentials found, setting up financials URL...');
      setCredentials({ username: storedUsername, password: storedPassword });

      // Create URL with embedded credentials - properly encode username and password
      const encodedUsername = encodeURIComponent(storedUsername);
      const encodedPassword = encodeURIComponent(storedPassword);
      const urlWithCredentials = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Financial/BalanceView_001.aspx`;
      setFinancialsUrl(urlWithCredentials);

      console.log('🎯 Financials URL prepared');
      // Only hide loading if we're not doing background refresh or don't have cached data
      if (!isBackgroundRefresh && !hasCachedData) {
        setIsLoading(false);
        // Start loading financials immediately if no cached data
        setIsLoadingFinancials(true);
      }

    } catch (error) {
      console.error('❌ Error loading credentials:', error);
      if (!isBackgroundRefresh) {
        // Alert.alert(
        //   'Error',
        //   'Failed to load credentials. Please try again.',
        //   [
        //     {
        //       text: 'Go Back',
        //       onPress: () => navigation.goBack()
        //     }
        //   ]
        // );
      }
      setIsUpdatingData(false);
    }
  };

  const handleRefresh = async () => {
    console.log('🔄 Refresh button pressed - reloading financials');

    // Check for demo mode
    const demoHandled = await handleDemoModeRefresh(
      startRotationAnimation,
      stopRotationAnimation
    );

    if (demoHandled) {
      return;
    }

    // Clear cache to force fresh data
    await clearCache();

    // Clear current data and start loading
    setIsLoadingFinancials(true);
    setFinancialsData([]);
    setHasCachedData(false);
    setHasDataBeenFetched(false); // Reset data fetched flag
    setIsUpdatingData(false); // Don't show update indicator during manual refresh

    // Start rotation animation
    startRotationAnimation();

    // Reload the WebView
    if (webViewRef.current && financialsUrl) {
      webViewRef.current.reload();
    }
  };

  const handleWebViewLoad = () => {
    console.log('💰 Financials page loaded successfully');

    // Loading state should already be set when URL was prepared
    // Only set it here if we somehow missed it and have no cached data
    if (!hasCachedData && !isLoadingFinancials) {
      setIsLoadingFinancials(true);
    }

    // Add a delay to ensure the page is fully loaded (optimized)
    safeSetTimeout(() => {
      extractFinancialsData();
    }, 1000); // Optimized from 2 seconds to 1 second
  };

  const extractFinancialsData = () => {
    if (webViewRef.current) {
      const jsCode = `
        function extractFinancialsData() {
          try {
            // Find the table by its ID
            const table = document.getElementById('ContentPlaceHolderright_ContentPlaceHoldercontent_DG_PaymentRequest');

            if (!table) {
              if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                  type: 'financials_data',
                  error: 'No financial data found',
                  isEmpty: true
                }));
              }
              return;
            }

            const rows = table.querySelectorAll('tr');
            const financialsData = [];

            // Skip the header row (index 0)
            for (let i = 1; i < rows.length; i++) {
              const cells = rows[i].querySelectorAll('td');
              if (cells.length >= 5) {
                const paymentData = {
                  paymentDescription: cells[1].textContent.trim(),
                  currency: cells[2].textContent.trim(),
                  amount: cells[3].textContent.trim(),
                  dueDate: cells[4].textContent.trim()
                };
                financialsData.push(paymentData);
              }
            }

            if (window.ReactNativeWebView) {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'financials_data',
                data: financialsData,
                totalPayments: financialsData.length
              }));
            }
          } catch (error) {
            if (window.ReactNativeWebView) {
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'financials_data',
                error: error.message
              }));
            }
          }
        }

        extractFinancialsData();
      `;

      webViewRef.current.injectJavaScript(jsCode);
    }
  };

  const handleWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('📨 Received message from WebView:', data);

      if (data.type === 'financials_data') {
        if (data.error) {
          console.log('❌ Financials extraction error:', data.error);
          setIsLoadingFinancials(false);
          setIsUpdatingData(false); // Stop update indicator on error
          stopRotationAnimation();

          if (data.isEmpty) {
            // No financial data found - this is normal, not an error
            setFinancialsData([]);
            setHasDataBeenFetched(true); // Mark that we've confirmed no data
            saveToCache([]); // Cache empty result
          }
        } else {
          console.log('💰 Financials data extracted!');
          console.log('📊 Total payments found:', data.totalPayments);
          console.log('📊 Financials data:', data.data);

          safeSetState(setFinancialsData, data.data, 'financialsData');
          safeSetState(setHasDataBeenFetched, true, 'hasDataBeenFetched'); // Mark that we've fetched data
          safeSetState(setIsLoadingFinancials, false, 'isLoadingFinancials');
          safeSetState(setIsUpdatingData, false, 'isUpdatingData'); // Stop update indicator
          stopRotationAnimation();

          // Save to cache when data is successfully extracted
          saveToCache(data.data);
        }
      }
    } catch (error) {
      console.log('❌ Error parsing WebView message:', error);
      safeSetState(setIsLoadingFinancials, false, 'isLoadingFinancials');
      safeSetState(setIsUpdatingData, false, 'isUpdatingData');
      stopRotationAnimation();
    }
  };

  const handleWebViewError = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.log('❌ WebView error:', nativeEvent);
    setIsLoadingFinancials(false);
    setIsUpdatingData(false);
    stopRotationAnimation();
  };

  // Create styles with theme
  const styles = createStyles(theme, safeCurrentThemeName);

  return (
    <View style={{ flex: 1 }} {...swipeGestureHandler.panHandlers}>
      <SafeAreaView style={styles.container}>
      {/* Sidebar Button */}
      <View style={styles.sidebarButtonContainer}>
        <TouchableOpacity
          style={styles.sidebarButton}
          onPress={openSidebar}
        >
          <HamburgerIcon size={20} color={theme.colors.primary} strokeWidth={3} />
        </TouchableOpacity>
      </View>

      {/* Page Title */}
      <View style={styles.titleContainer}>
        <Text style={styles.title}>Financials</Text>
      </View>

      {/* Refresh Button */}
      <View style={styles.refreshButtonContainer}>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefresh}
        >
          <Animated.View
            style={{
              transform: [{
                rotate: refreshRotation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg']
                })
              }]
            }}
          >
            <RefreshIcon
              size={24}
              color={(isLoadingFinancials || isUpdatingData) ? '#808080' : safeCurrentThemeName === 'navy' ? '#DC2626' : '#f1c40f'}
            />
          </Animated.View>
        </TouchableOpacity>
      </View>

      {/* Main Content */}
      <View style={styles.mainContent}>
        {(isLoading && !hasCachedData) ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Loading financials...</Text>
          </View>
        ) : financialsUrl ? (
          <>
            {/* Financials Display Section */}
            {(isLoadingFinancials && !hasCachedData) ? (
              <View style={styles.financialsLoadingContainer}>
                <ActivityIndicator size="large" color={theme.colors.primary} />
                <Text style={styles.loadingText}>Loading financial data...</Text>
              </View>
            ) : financialsData.length > 0 ? (
              <ScrollView style={styles.financialsContainer} showsVerticalScrollIndicator={false}>
                <Text style={styles.sectionTitle}>Outstanding Payments</Text>
                {financialsData.map((payment, index) => (
                  <View key={index} style={styles.paymentCard}>
                    <View style={styles.paymentHeader}>
                      <Text style={styles.paymentDescription}>{payment.paymentDescription}</Text>
                      <View style={styles.amountContainer}>
                        <Text style={styles.amount}>{payment.amount}</Text>
                        <Text style={styles.currency}>{payment.currency}</Text>
                      </View>
                    </View>
                    <Text style={styles.dueDate}>Due: {formatDate(payment.dueDate)}</Text>
                  </View>
                ))}
              </ScrollView>
            ) : hasDataBeenFetched ? (
              <View style={styles.noDataContainer}>
                <Text style={styles.noDataText}>🎉 No Outstanding Payments</Text>
                <Text style={styles.noDataSubtext}>
                  You don't have any pending installments or payments at the moment.
                </Text>
              </View>
            ) : null}

            {/* Hidden WebView */}
            <View style={styles.hiddenWebView}>
              {financialsUrl && !financialsUrl.startsWith('demo://') && (
                <WebView
                  ref={webViewRef}
                  source={{ uri: financialsUrl }}
                  onLoad={handleWebViewLoad}
                  onMessage={handleWebViewMessage}
                  onError={handleWebViewError}
                  javaScriptEnabled={true}
                  domStorageEnabled={true}
                  mixedContentMode="compatibility"
                  cacheEnabled={false}
                  incognito={true}
                  userAgent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
                />
              )}
            </View>
          </>
        ) : (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={theme.colors.primary} />
            <Text style={styles.loadingText}>Setting up financials...</Text>
          </View>
        )}
      </View>

      {/* Sidebar Component */}
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={closeSidebar}
        sidebarAnim={sidebarAnim}
        navigation={navigation}
        currentScreen="Financials"
      />
    </SafeAreaView>
    </View>
  );
};

// Move styles inside component to access theme
const createStyles = (theme, safeCurrentThemeName) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  titleContainer: {
    position: 'absolute',
    top: 55, // Raised higher
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 999,
  },
  title: {
    fontSize: 30, // Increased font size slightly
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  sidebarButtonContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 1000,
  },
  sidebarButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  refreshButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1000,
  },
  refreshButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  mainContent: {
    flex: 1,
    paddingTop: 100,
    paddingHorizontal: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: theme.colors.primary,
    fontSize: 16,
    marginTop: 10,
  },
  financialsLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  financialsContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 20,
    textAlign: 'center',
  },
  paymentCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.border,
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  paymentDescription: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    flex: 1,
    marginRight: 10,
  },
  amountContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  amount: {
    fontSize: 20,
    fontWeight: 'bold',
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.primary,
    marginRight: 4,
  },
  currency: {
    fontSize: 14,
    color: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.textSecondary,
    fontWeight: '500',
  },
  dueDate: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  noDataText: {
    fontSize: 20,
    color: theme.colors.text,
    textAlign: 'center',
    marginBottom: 10,
    fontWeight: '600',
  },
  noDataSubtext: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
});

export default FinancialsScreen;

