import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Centralized cache manager that handles demo mode isolation
 * Prevents demo data from contaminating real user data and vice versa
 */

/**
 * Check if current user is in demo mode
 * @returns {Promise<boolean>}
 */
const isDemoMode = async () => {
  try {
    const demoFlag = await AsyncStorage.getItem('is_demo_user');
    return demoFlag === 'true';
  } catch (error) {
    console.log('❌ Error checking demo mode:', error);
    return false;
  }
};

/**
 * Generate cache key with demo mode prefix
 * @param {string} baseKey - Base cache key (e.g., 'exams_cache')
 * @returns {Promise<string>} - Prefixed cache key
 */
const getCacheKey = async (baseKey) => {
  const isDemo = await isDemoMode();
  const prefix = isDemo ? 'demo_' : 'real_';
  return `${prefix}${baseKey}`;
};

/**
 * Save data to cache with demo mode isolation
 * @param {string} baseKey - Base cache key
 * @param {any} data - Data to cache
 * @param {Object} metadata - Additional metadata (optional)
 * @returns {Promise<boolean>} - Success status
 */
export const saveToCache = async (baseKey, data, metadata = {}) => {
  try {
    const isDemo = await isDemoMode();
    const cacheKey = await getCacheKey(baseKey);
    
    const cacheData = {
      data: data,
      timestamp: Date.now(),
      isDemoData: isDemo,
      ...metadata
    };
    
    await AsyncStorage.setItem(cacheKey, JSON.stringify(cacheData));
    console.log(`📦 Data cached successfully: ${cacheKey} (${isDemo ? 'demo' : 'real'} mode)`);
    return true;
  } catch (error) {
    console.log(`❌ Error saving to cache (${baseKey}):`, error);
    return false;
  }
};

/**
 * Load data from cache with demo mode validation
 * @param {string} baseKey - Base cache key
 * @returns {Promise<Object|null>} - Cached data or null if not found/invalid
 */
export const loadFromCache = async (baseKey) => {
  try {
    const isDemo = await isDemoMode();
    const cacheKey = await getCacheKey(baseKey);
    const cachedData = await AsyncStorage.getItem(cacheKey);
    
    if (cachedData) {
      const parsedData = JSON.parse(cachedData);
      
      // Verify that cached data matches current mode
      const cachedIsDemoData = parsedData.isDemoData === true;
      const currentIsDemoMode = isDemo;
      
      if (cachedIsDemoData !== currentIsDemoMode) {
        console.log(`🚫 Cache mode mismatch for ${baseKey}. Cached: ${cachedIsDemoData ? 'demo' : 'real'}, Current: ${currentIsDemoMode ? 'demo' : 'real'}. Clearing cache.`);
        await AsyncStorage.removeItem(cacheKey);
        return null;
      }
      
      console.log(`📦 Loading cached data: ${cacheKey} (${currentIsDemoMode ? 'demo' : 'real'} mode)`);
      return parsedData;
    }
    
    console.log(`📭 No cached data found: ${baseKey}`);
    return null;
  } catch (error) {
    console.log(`❌ Error loading from cache (${baseKey}):`, error);
    return null;
  }
};

/**
 * Clear specific cache entry
 * @param {string} baseKey - Base cache key
 * @returns {Promise<boolean>} - Success status
 */
export const clearCache = async (baseKey) => {
  try {
    const cacheKey = await getCacheKey(baseKey);
    await AsyncStorage.removeItem(cacheKey);
    console.log(`🗑️ Cache cleared: ${cacheKey}`);
    return true;
  } catch (error) {
    console.log(`❌ Error clearing cache (${baseKey}):`, error);
    return false;
  }
};

/**
 * Clear all caches (both demo and real) for a specific base key
 * @param {string} baseKey - Base cache key
 * @returns {Promise<boolean>} - Success status
 */
export const clearAllCachesForKey = async (baseKey) => {
  try {
    const demoKey = `demo_${baseKey}`;
    const realKey = `real_${baseKey}`;
    
    await AsyncStorage.multiRemove([demoKey, realKey]);
    console.log(`🧹 All caches cleared for: ${baseKey}`);
    return true;
  } catch (error) {
    console.log(`❌ Error clearing all caches for ${baseKey}:`, error);
    return false;
  }
};

/**
 * Clear all app caches (demo and real) - use when switching accounts
 * @returns {Promise<boolean>} - Success status
 */
export const clearAllAppCaches = async () => {
  try {
    console.log('🧹 Clearing all app caches...');
    const keys = await AsyncStorage.getAllKeys();
    
    // Find all cache keys (both demo_ and real_ prefixed)
    const cacheKeys = keys.filter(key => 
      key.startsWith('demo_') || 
      key.startsWith('real_') ||
      // Legacy cache keys without prefixes
      key.includes('_cache') ||
      key.includes('_timestamp')
    );
    
    if (cacheKeys.length > 0) {
      await AsyncStorage.multiRemove(cacheKeys);
      console.log(`🧹 Cleared ${cacheKeys.length} cache entries`);
    }
    
    return true;
  } catch (error) {
    console.log('❌ Error clearing all app caches:', error);
    return false;
  }
};

/**
 * Get cache statistics for debugging
 * @returns {Promise<Object>} - Cache statistics
 */
export const getCacheStats = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const demoCacheKeys = keys.filter(key => key.startsWith('demo_'));
    const realCacheKeys = keys.filter(key => key.startsWith('real_'));
    const legacyCacheKeys = keys.filter(key => 
      !key.startsWith('demo_') && 
      !key.startsWith('real_') && 
      (key.includes('_cache') || key.includes('_timestamp'))
    );
    
    return {
      totalCacheKeys: demoCacheKeys.length + realCacheKeys.length + legacyCacheKeys.length,
      demoCacheKeys: demoCacheKeys.length,
      realCacheKeys: realCacheKeys.length,
      legacyCacheKeys: legacyCacheKeys.length,
      currentMode: await isDemoMode() ? 'demo' : 'real'
    };
  } catch (error) {
    console.log('❌ Error getting cache stats:', error);
    return null;
  }
};
