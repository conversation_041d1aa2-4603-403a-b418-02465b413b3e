{"cli": {"version": ">= 16.14.1", "appVersionSource": "remote"}, "build": {"debug": {"distribution": "internal", "android": {"buildType": "apk", "gradleCommand": ":app:assembleDebug"}, "ios": {"buildConfiguration": "Debug"}, "developmentClient": true, "env": {"NODE_ENV": "development"}}, "development": {"distribution": "internal", "ios": {"buildConfiguration": "Debug", "developmentClient": true}, "developmentClient": true, "env": {"NODE_ENV": "development"}}, "preview": {"distribution": "internal", "android": {"buildType": "apk"}, "ios": {"buildConfiguration": "Release", "distribution": "internal", "image": "latest"}, "env": {"NODE_ENV": "production"}}, "production": {"android": {"buildType": "apk", "gradleCommand": ":app:assembleRelease"}, "ios": {"buildConfiguration": "Release", "distribution": "store", "image": "latest"}, "autoIncrement": true, "env": {"NODE_ENV": "production"}}}}