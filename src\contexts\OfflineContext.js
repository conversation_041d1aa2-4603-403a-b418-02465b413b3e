import React, { createContext, useContext, useState } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { isDemoUser } from '../utils/DemoData';

const OfflineContext = createContext();

export const useOffline = () => {
  const context = useContext(OfflineContext);
  if (!context) {
    throw new Error('useOffline must be used within an OfflineProvider');
  }
  return context;
};

export const OfflineProvider = ({ children }) => {
  const [isOfflineMode, setIsOfflineMode] = useState(false);

  // Simple network connectivity check for login attempts only
  const checkNetworkConnectivity = async () => {
    try {
      // Skip network check for demo users to avoid unnecessary network calls
      const demoFlag = await AsyncStorage.getItem('is_demo_user');
      if (demoFlag === 'true') {
        console.log('🎭 Demo mode detected - skipping network connectivity check');
        return true; // Always return true for demo users
      }
      
      // Try to fetch a small resource from a reliable server
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch('https://dns.google/resolve?name=google.com&type=A', {
        method: 'GET',
        signal: controller.signal,
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      clearTimeout(timeoutId);
      const hasConnection = response.ok;

      console.log('🌐 Network connectivity check:', {
        status: response.status,
        ok: response.ok,
        hasConnection
      });

      return hasConnection;
    } catch (error) {
      console.log('❌ Network connectivity check failed:', error.message);
      return false;
    }
  };

  // Enter offline mode
  const enterOfflineMode = async () => {
    console.log('📴 Entering offline mode...');
    setIsOfflineMode(true);
    await AsyncStorage.setItem('is_offline_mode', 'true');
  };

  // Exit offline mode
  const exitOfflineMode = async () => {
    console.log('🌐 Exiting offline mode...');
    setIsOfflineMode(false);
    await AsyncStorage.removeItem('is_offline_mode');
  };

  // Check if currently in offline mode (persisted)
  const checkOfflineMode = async () => {
    try {
      // Skip offline mode check for demo users - they should never be in offline mode
      const demoFlag = await AsyncStorage.getItem('is_demo_user');
      if (demoFlag === 'true') {
        console.log('🎭 Demo mode detected - skipping offline mode check (demo users are never offline)');
        setIsOfflineMode(false);
        return false;
      }
      
      const offlineFlag = await AsyncStorage.getItem('is_offline_mode');
      const isOffline = offlineFlag === 'true';
      setIsOfflineMode(isOffline);
      return isOffline;
    } catch (error) {
      console.log('❌ Error checking offline mode:', error);
      return false;
    }
  };

  // Check if a feature is available in offline mode
  const isFeatureAvailable = (featureName) => {
    if (!isOfflineMode) return true;

    // Features that are never available in offline mode
    const unavailableFeatures = ['cms', 'mail', 'evaluate'];
    if (unavailableFeatures.includes(featureName.toLowerCase())) {
      return false;
    }

    return true;
  };

  // Show offline mode popup for unavailable features
  const showOfflinePopup = () => {
    // This will be implemented in the UI components
    return {
      title: 'Feature Unavailable',
      message: 'This feature is not available in offline mode. Please go online to access it.',
      buttonText: 'OK'
    };
  };

  const value = {
    isOfflineMode,
    enterOfflineMode,
    exitOfflineMode,
    checkOfflineMode,
    checkNetworkConnectivity,
    isFeatureAvailable,
    showOfflinePopup
  };

  return (
    <OfflineContext.Provider value={value}>
      {children}
    </OfflineContext.Provider>
  );
};
