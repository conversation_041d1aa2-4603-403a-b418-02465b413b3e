import { registerRootComponent } from 'expo';
import 'react-native-gesture-handler';
import { Platform } from 'react-native';

import App from './App';

// Enable Hermes debugging and crash reporting for production builds
if (__DEV__) {
  console.log('🔧 Development mode - Full debugging enabled');
} else {
  console.log('🚀 Production mode - Hermes debugging enabled');

  // Enable global error handling for production
  const originalConsoleError = console.error;
  console.error = (...args) => {
    // Log to console for debugging
    originalConsoleError(...args);

    // You can add crash reporting service here (like Sentry, Bugsnag, etc.)
    // Example: Sentry.captureException(new Error(args.join(' ')));
  };

  // Global error handler for unhandled promise rejections
  const handleUnhandledRejection = (event) => {
    console.error('🚨 Unhandled Promise Rejection:', event.reason);
    // You can add crash reporting here
    // Example: Sentry.captureException(event.reason);
  };

  // Global error handler for JavaScript errors
  const handleGlobalError = (error, isFatal) => {
    console.error('🚨 Global JavaScript Error:', error);
    console.error('🚨 Is Fatal:', isFatal);
    console.error('🚨 Stack Trace:', error.stack);

    // You can add crash reporting here
    // Example: Sentry.captureException(error);

    if (isFatal) {
      console.error('🚨 Fatal error detected - App may crash');
    }
  };

  // Set up global error handlers
  if (typeof global !== 'undefined') {
    global.addEventListener?.('unhandledrejection', handleUnhandledRejection);

    // For React Native global error handling
    if (global.ErrorUtils) {
      global.ErrorUtils.setGlobalHandler(handleGlobalError);
    }
  }

  // Enable Hermes debugging features
  if (global.HermesInternal) {
    console.log('✅ Hermes engine detected');
    console.log('📊 Hermes version:', global.HermesInternal.getRuntimeProperties?.()?.['OSS Release Version'] || 'Unknown');

    // Enable additional Hermes debugging
    if (global.HermesInternal.enableDebugger) {
      console.log('🔍 Hermes debugger capabilities available');
    }
  } else {
    console.log('⚠️ Hermes engine not detected - using JSC');
  }
}

// registerRootComponent calls AppRegistry.registerComponent('main', () => App);
// It also ensures that whether you load the app in Expo Go or in a native build,
// the environment is set up appropriately
registerRootComponent(App);
