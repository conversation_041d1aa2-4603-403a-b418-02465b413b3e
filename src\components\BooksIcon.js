import React from 'react';
import Svg, { Rect, Line, G } from 'react-native-svg';

const BooksIcon = ({ size = 24, color = '#000000' }) => {
  return (
    <Svg 
      width={size} 
      height={size} 
      viewBox="0 0 64 64" 
      fill="none"
    >
      {/* Book 1 (Yellow) */}
      <Rect 
        x="8" 
        y="12" 
        width="12" 
        height="40" 
        rx="2" 
        fill="#FFD966" 
        stroke="#000" 
        strokeWidth="2"
      />
      <Line 
        x1="10" 
        y1="20" 
        x2="18" 
        y2="20" 
        stroke="#000" 
        strokeWidth="2" 
        strokeLinecap="round"
      />
      <Line 
        x1="10" 
        y1="28" 
        x2="18" 
        y2="28" 
        stroke="#000" 
        strokeWidth="2" 
        strokeLinecap="round"
      />
      
      {/* Book 2 (Red) */}
      <Rect 
        x="20" 
        y="12" 
        width="12" 
        height="40" 
        rx="2" 
        fill="#FF6666" 
        stroke="#000" 
        strokeWidth="2"
      />
      <Line 
        x1="22" 
        y1="20" 
        x2="30" 
        y2="20" 
        stroke="#000" 
        strokeWidth="2" 
        strokeLinecap="round"
      />
      <Line 
        x1="22" 
        y1="28" 
        x2="30" 
        y2="28" 
        stroke="#000" 
        strokeWidth="2" 
        strokeLinecap="round"
      />

      {/* Book 3 (Blue) - moved UP and LEFT slightly */}
      <G transform="rotate(-15 48 50)">
        <Rect 
          x="42" 
          y="12" 
          width="12" 
          height="40" 
          rx="2" 
          fill="#6699FF" 
          stroke="#000" 
          strokeWidth="2"
        />
        <Line 
          x1="44" 
          y1="20" 
          x2="52" 
          y2="20" 
          stroke="#000" 
          strokeWidth="2" 
          strokeLinecap="round"
        />
        <Line 
          x1="44" 
          y1="28" 
          x2="52" 
          y2="28" 
          stroke="#000" 
          strokeWidth="2" 
          strokeLinecap="round"
        />
      </G>
    </Svg>
  );
};

export default BooksIcon;
