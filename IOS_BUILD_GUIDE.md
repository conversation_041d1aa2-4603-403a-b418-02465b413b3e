# iOS Production Build Guide for myGUC App

## Overview

This guide will help you build an iOS production version of your myGUC app that matches your current Expo Go experience.

## Key Changes Made

### 1. iOS-Specific Logo

- **iOS**: Uses `myGUCLogomini1.png` for a fuller appearance
- **Android**: Keeps `myGUCLogomini.png` unchanged
- **Splash Screen**: iOS also uses the fuller logo

### 2. Network Security Configuration

The app now properly allows access to these domains:

- `apps.guc.edu.eg` - Student portal
- `cms.guc.edu.eg` - CMS system
- `mail.guc.edu.eg` - Email system
- `u.expo.dev` - Expo updates

### 3. iOS Build Configuration

- Bundle Identifier: `com.myguc.app`
- Deployment Target: iOS 15.1+
- Static frameworks for better compatibility

## Prerequisites

### 1. EAS CLI Installation

```bash
npm install -g @expo/eas-cli
```

### 2. Apple Developer Account

- Active Apple Developer Program membership ($99/year)
- App Store Connect access
- Valid provisioning profiles and certificates

### 3. Xcode (Optional but Recommended)

- Latest Xcode version for local testing
- iOS Simulator for testing

## Build Commands

### Debug Build (Internal Testing)

```bash
npm run build:ios:debug
```

### Preview Build (Internal Distribution)

```bash
npm run build:ios:preview
```

### Production Build (App Store)

```bash
npm run build:ios:production
```

## Build Process

### 1. First Time Setup

```bash
# Login to your Expo account
eas login

# Configure your project
eas build:configure

# Select iOS platform when prompted
```

### 2. Build Configuration

The build will:

- Use the iOS-specific logo (`myGUCLogomini1.png`)
- Apply network security settings for GUC domains
- Generate proper iOS bundle with all necessary permissions
- Create a production-ready IPA file

### 3. Build Output

- **Debug**: `.tar.gz` file for internal testing
- **Preview**: `.tar.gz` file for internal distribution
- **Production**: `.tar.gz` file ready for App Store submission

## Testing Your Build

### 1. Internal Testing

- Use TestFlight for internal distribution
- Upload the build to App Store Connect
- Invite testers via email

### 2. Local Testing

- Download the build artifact
- Install on physical iOS device
- Test all GUC portal functionality

## Common Issues & Solutions

### 1. Network Security

If you encounter network issues:

- Verify the domain is in `NSExceptionDomains`
- Check TLS version requirements
- Ensure proper certificate validation

### 2. Build Failures

- Clear cache: `--clear-cache` flag
- Check EAS build logs for specific errors
- Verify Apple Developer account status

### 3. App Store Rejection

- Ensure all domains are properly configured
- Verify privacy policy compliance
- Check for any deprecated API usage

## App Store Submission

### 1. Prepare for Submission

- Test thoroughly on multiple devices
- Verify all GUC portal features work
- Check network connectivity in various conditions

### 2. Submit to App Store

- Upload build via App Store Connect
- Fill out app metadata and screenshots
- Submit for review

## Maintenance

### 1. Regular Updates

- Keep EAS CLI updated
- Monitor for iOS compatibility issues
- Update dependencies as needed

### 2. Version Management

- Use semantic versioning
- Test each version thoroughly
- Maintain changelog for users

## Support

For build issues:

1. Check EAS build logs
2. Review Apple Developer documentation
3. Contact Expo support if needed

## Notes

- The iOS build will be significantly larger than Android due to static frameworks
- Build times may vary based on EAS server load
- Always test production builds before App Store submission
- Keep your Apple Developer account active for continued builds
