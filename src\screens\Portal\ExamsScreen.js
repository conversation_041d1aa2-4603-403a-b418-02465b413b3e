import React, { useState, useRef, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Animated,
  PanResponder,
  Easing,
} from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import { SafeAreaView } from 'react-native-safe-area-context';
import { WebView } from 'react-native-webview';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useFocusEffect } from '@react-navigation/native';
import Sidebar from '../../components/Sidebar';
import HamburgerIcon from '../../components/HamburgerIcon';
import RefreshIcon from '../../components/RefreshIcon';
import { clearWebViewSession, disposeWebView } from '../../utils/WebViewUtils';
import { useTheme } from '../../contexts/ThemeContext';
import { isDemoMode, getDemoExamsData, simulateLoading } from '../../utils/DemoModeHelper';


// Animated Exam Card Component
const AnimatedExamCard = ({ exam, index, styles, theme, safeCurrentThemeName }) => {
  // Safety check for exam data
  if (!exam || typeof exam !== 'object') {
    console.log('AnimatedExamCard: Invalid exam data:', exam);
    return null;
  }

  const slideAnim = useRef(new Animated.Value(40)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    // Staggered entrance animation
    const delay = index * 120; // 120ms delay between each card

    Animated.parallel([
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        delay,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
      Animated.timing(opacityAnim, {
        toValue: 1,
        duration: 600,
        delay,
        easing: Easing.out(Easing.ease),
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        delay: delay + 200,
        tension: 100,
        friction: 8,
        useNativeDriver: true,
      }),
    ]).start();
  }, [index]);

  // Calculate exam timing info with enhanced status detection
  const calculateExamStatus = (examDate, startTime, endTime) => {
    console.log('🔍 calculateExamStatus called with:', { examDate, startTime, endTime });

    if (!examDate || typeof examDate !== 'string') {
      console.log('❌ Invalid examDate:', examDate);
      return { days: 0, isPassed: true, isToday: false, status: 'passed', isNow: false };
    }

    try {
      const now = new Date();
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      console.log('🕐 Current time:', now.toLocaleString());
      console.log('📅 Today (midnight):', today.toLocaleString());

      let exam;

      // Handle DD/MM/YYYY format first (like "02/08/2025")
      if (examDate.includes('/')) {
        const parts = examDate.split('/');
        if (parts.length === 3) {
          const day = parseInt(parts[0]);
          const month = parseInt(parts[1]) - 1; // Month is 0-based in Date
          const year = parseInt(parts[2]);
          exam = new Date(year, month, day);
          console.log('📅 Parsed DD/MM/YYYY format:', { day, month: month + 1, year, result: exam.toDateString() });
        }
      }

      // Fallback approach: Try to parse as-is first (for formats like "15/01/2024")
      if (!exam || isNaN(exam.getTime())) {
        exam = new Date(examDate);
        console.log('📅 Tried parsing as-is:', exam.toDateString());
      }

      // Fallback approach: Handle format like "2 - August - 2025"
      if (isNaN(exam.getTime()) && examDate.includes(' - ')) {
        const parts = examDate.split(' - ');
        if (parts.length >= 3) {
          const day = parts[0] ? parts[0].trim() : '';
          const monthName = parts[1] ? parts[1].trim() : '';
          const year = parts[2] ? parts[2].trim() : '';

          if (day && monthName && year) {
            // Convert month name to number
            const monthMap = {
              'January': 0, 'Jan': 0,
              'February': 1, 'Feb': 1,
              'March': 2, 'Mar': 2,
              'April': 3, 'Apr': 3,
              'May': 4,
              'June': 5, 'Jun': 5,
              'July': 6, 'Jul': 6,
              'August': 7, 'Aug': 7,
              'September': 8, 'Sep': 8,
              'October': 9, 'Oct': 9,
              'November': 10, 'Nov': 10,
              'December': 11, 'Dec': 11
            };

            const monthNum = monthMap[monthName];
            if (monthNum !== undefined) {
              // Create Date object with proper month number (0-based)
              exam = new Date(parseInt(year), monthNum, parseInt(day));
            }
          }
        }
      }

      if (isNaN(exam.getTime())) {
        return { days: 0, isPassed: true, isToday: false, status: 'passed', isNow: false };
      }

      exam.setHours(0, 0, 0, 0);

      const diffTime = exam - today;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

      const isToday = diffDays === 0;
      const isPassed = diffDays < 0;

      console.log('📊 Date calculation:', {
        examDate: exam.toDateString(),
        today: today.toDateString(),
        diffDays,
        isToday,
        isPassed
      });

      // If it's today, check the current time against exam times
      let isNow = false;
      let status = 'upcoming';

      if (isToday && startTime && endTime) {
        // Parse exam times
        const parseExamTime = (timeStr) => {
          if (!timeStr || typeof timeStr !== 'string') return null;

          let cleanTime = timeStr.trim();
          let isPM = false;

          // Check for AM/PM
          if (cleanTime.toLowerCase().includes('pm')) {
            isPM = true;
            cleanTime = cleanTime.replace(/\s*pm\s*/i, '');
          } else if (cleanTime.toLowerCase().includes('am')) {
            cleanTime = cleanTime.replace(/\s*am\s*/i, '');
          }

          // Split by colon to get hours, minutes, seconds
          const timeParts = cleanTime.split(':');
          const hours = parseInt(timeParts[0]) || 0;
          const minutes = parseInt(timeParts[1]) || 0;

          // Convert to 24-hour format
          let hour24 = hours;
          if (isPM && hours !== 12) {
            hour24 = hours + 12;
          } else if (!isPM && hours === 12) {
            hour24 = 0;
          }

          // Create a date object for today with the exam time
          const examDateTime = new Date();
          examDateTime.setHours(hour24, minutes, 0, 0);
          return examDateTime;
        };

        const examStartTime = parseExamTime(startTime);
        const examEndTime = parseExamTime(endTime);

        if (examStartTime && examEndTime) {
          if (now >= examStartTime && now <= examEndTime) {
            // Currently during the exam
            isNow = true;
            status = 'now';
          } else if (now > examEndTime) {
            // Exam has ended today
            status = 'passed';
          } else {
            // Exam is today but hasn't started yet
            status = 'today';
          }
        } else {
          // Can't parse times, default to today status
          status = 'today';
        }
      } else if (isPassed) {
        status = 'passed';
      } else {
        status = 'upcoming';
      }

      const result = {
        days: Math.abs(diffDays),
        isPassed,
        isToday,
        status,
        isNow
      };

      console.log('✅ Final exam status:', result);
      return result;
    } catch (error) {
      console.log('Error in calculateExamStatus:', error);
      return { days: 0, isPassed: true, isToday: false, status: 'passed', isNow: false };
    }
  };

  const calculateDuration = (startTime, endTime) => {
    if (!startTime || !endTime || typeof startTime !== 'string' || typeof endTime !== 'string') {
      return { duration: 'Unknown', hours: 0, minutes: 0 };
    }

    try {
      // Parse time strings like "1:00:00 PM" and "4:00:00 PM"
      const parseTime = (timeStr) => {
        if (!timeStr || typeof timeStr !== 'string') {
          return { hours: 0, minutes: 0 };
        }

        let cleanTime = timeStr.trim();
        let isPM = false;

        // Check for AM/PM
        if (cleanTime.toLowerCase().includes('pm')) {
          isPM = true;
          cleanTime = cleanTime.replace(/\s*pm\s*/i, '');
        } else if (cleanTime.toLowerCase().includes('am')) {
          cleanTime = cleanTime.replace(/\s*am\s*/i, '');
        }

        // Split by colon to get hours, minutes, seconds
        const timeParts = cleanTime.split(':');
        const hours = parseInt(timeParts[0]) || 0;
        const minutes = parseInt(timeParts[1]) || 0;

        // Convert to 24-hour format
        let hour24 = hours;
        if (isPM && hours !== 12) {
          hour24 = hours + 12;
        } else if (!isPM && hours === 12) {
          hour24 = 0;
        }

        return { hours: hour24, minutes: minutes };
      };

      const start = parseTime(startTime);
      const end = parseTime(endTime);

      // Calculate duration in minutes
      const startMinutes = start.hours * 60 + start.minutes;
      const endMinutes = end.hours * 60 + end.minutes;
      let durationMinutes = endMinutes - startMinutes;

      // Handle case where end time is next day (like 11 PM to 2 AM)
      if (durationMinutes < 0) {
        durationMinutes += 24 * 60;
      }

      const durationHours = Math.floor(durationMinutes / 60);
      const remainingMinutes = durationMinutes % 60;

      // Format start time (convert back to 12-hour format for display)
      let displayHour = start.hours;
      let ampm = 'AM';
      if (displayHour === 0) {
        displayHour = 12;
      } else if (displayHour === 12) {
        ampm = 'PM';
      } else if (displayHour > 12) {
        displayHour = displayHour - 12;
        ampm = 'PM';
      }

      const formattedStartTime = `${displayHour}:${start.minutes.toString().padStart(2, '0')} ${ampm}`;

      // Format duration
      let duration = '';
      if (durationHours > 0) {
        duration = `${durationHours}h`;
        if (remainingMinutes > 0) {
          duration += ` ${remainingMinutes}m`;
        }
      } else {
        duration = `${remainingMinutes}m`;
      }

      return {
        duration,
        startTime: formattedStartTime,
        hours: durationHours,
        minutes: remainingMinutes
      };
    } catch (error) {
      console.log('Error calculating duration:', error);
      return { duration: 'Unknown', startTime: startTime || 'Unknown', hours: 0, minutes: 0 };
    }
  };

  const formatCourseName = (courseNameInput) => {
    if (!courseNameInput || typeof courseNameInput !== 'string') {
      return { main: '', code: '' };
    }

    try {
      // Step 1: Extract course code first
      const codeMatch = courseNameInput.match(/([A-Z]{4}\d{3})/);
      const courseCode = codeMatch ? codeMatch[1] : '';

      if (courseCode) {
        // Step 2: Split the string by ' - ' to get individual parts
        const parts = courseNameInput.split(' - ');

        // Step 3: Filter out parts containing semester-related words
        const semesterWords = ['semester', 'summer', 'winter', 'spring', 'fall', 'autumn'];
        const goodParts = parts.filter(part => {
          if (!part || part.trim().length === 0) return false;

          const lowerPart = part.trim().toLowerCase();

          // Check if this part contains any semester-related words
          const containsSemesterWord = semesterWords.some(word =>
            lowerPart.includes(word)
          );

          return !containsSemesterWord;
        });

        // Step 4: From the remaining good parts, find the one with the course code and extract just the course name
        for (const part of goodParts) {
          if (part.includes(courseCode)) {
            // Remove the course code from this part to get just the course name
            let courseName = part.replace(courseCode, '').trim();

            // Clean up any leading/trailing separators
            courseName = courseName.replace(/^[\s\-]+|[\s\-]+$/g, '').trim();

            if (courseName && courseName.length > 0) {
              return { main: courseName, code: courseCode };
            }
          }
        }

        // Step 5: Fallback - if no part contains the course code, take the longest good part
        if (goodParts.length > 0) {
          const longestPart = goodParts
            .map(part => part.trim())
            .filter(part => part.length > 2)
            .sort((a, b) => b.length - a.length)[0];

          if (longestPart) {
            return { main: longestPart, code: courseCode };
          }
        }

        // Final fallback to course code
        return { main: courseCode, code: courseCode };
      }

      // If no course code found, try original approach
      const parts = courseNameInput.split(' - ');
      if (parts.length >= 2) {
        const originalMain = parts[0];
        const originalCode = parts[1];

        const codeMatch = originalCode.match(/([A-Z]{4}\d{3})/);
        if (codeMatch) {
          return { main: originalMain, code: codeMatch[1] };
        }
      }

      return { main: courseNameInput, code: '' };
    } catch (error) {
      console.log('Error in formatCourseName:', error);
      return { main: courseNameInput || '', code: '' };
    }
  };

  const formatDateDisplay = (dateStr) => {
    if (!dateStr || typeof dateStr !== 'string') return '';

    try {
      // Handle DD/MM/YYYY format (like "27/07/2025")
      if (dateStr.includes('/')) {
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          const day = parseInt(parts[0]);
          const month = parseInt(parts[1]) - 1; // Month is 0-based in Date
          const year = parseInt(parts[2]);

          if (!isNaN(day) && !isNaN(month) && !isNaN(year)) {
            const date = new Date(year, month, day);
            if (!isNaN(date.getTime())) {
              return date.toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric'
              });
            }
          }
        }
      }

      // Handle format like "2 - August - 2025"
      if (dateStr.includes(' - ')) {
        const parts = dateStr.split(' - ');
        if (parts.length >= 3) {
          const day = parts[0] ? parts[0].trim() : '';
          const monthName = parts[1] ? parts[1].trim() : '';
          const year = parts[2] ? parts[2].trim() : '';

          if (day && monthName && year) {
            // Convert month name to number
            const monthMap = {
              'January': 0, 'Jan': 0,
              'February': 1, 'Feb': 1,
              'March': 2, 'Mar': 2,
              'April': 3, 'Apr': 3,
              'May': 4,
              'June': 5, 'Jun': 5,
              'July': 6, 'Jul': 6,
              'August': 7, 'Aug': 7,
              'September': 8, 'Sep': 8,
              'October': 9, 'Oct': 9,
              'November': 10, 'Nov': 10,
              'December': 11, 'Dec': 11
            };

            const monthNum = monthMap[monthName];
            if (monthNum !== undefined) {
              // Create Date object with proper month number (0-based)
              const date = new Date(parseInt(year), monthNum, parseInt(day));

              if (!isNaN(date.getTime())) {
                return date.toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric'
                });
              }
            }
          }
        }
      }

      // Fallback: Try to parse as-is
      let date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        });
      }

      return dateStr;
    } catch (error) {
      console.log('Error in formatDateDisplay:', error);
      return dateStr || '';
    }
  };

  // Safely extract exam data with fallbacks
  const courseName = exam.Course || '';
  const examDate = exam.Date || '';
  const startTime = exam.StartTime || '';
  const endTime = exam.EndTime || '';
  const examDay = exam.Day || '';
  const examHall = exam.Hall || '';
  const examSeat = exam.Seat || '';

  // Debug logging
  console.log(`Exam ${index} data:`, {
    courseName,
    examDate,
    startTime,
    endTime,
    examDay,
    examHall,
    examSeat
  });

  const formatted = formatCourseName(courseName);
  const examStatus = calculateExamStatus(examDate, startTime, endTime);
  const timeInfo = calculateDuration(startTime, endTime);

  return (
    <Animated.View
      style={{
        transform: [
          { translateY: slideAnim },
          { scale: scaleAnim }
        ],
        opacity: opacityAnim,
      }}
    >
      <View style={[
        styles.examCard,
        examStatus.status === 'passed' && styles.examCardPassed,
        examStatus.status === 'today' && styles.examCardToday,
        examStatus.status === 'now' && styles.examCardNow,
        examStatus.status === 'upcoming' && styles.examCardUpcoming
      ]}>
        {/* Top Row: Course Name and Course Code Badge */}
        <View style={styles.examTopRow}>
          <Text style={[
            styles.examCourse,
            examStatus.status === 'passed' && styles.examTextPassed
          ]}>
            {formatted.main}
          </Text>
          <View style={[
            styles.examCodeBadge,
            examStatus.status === 'passed' && styles.examCodeBadgePassed
          ]}>
            <Text style={[
              styles.examCodeText,
              examStatus.status === 'passed' && styles.examCodeTextPassed
            ]}>
              {formatted.code}
            </Text>
          </View>
        </View>

        {/* Middle Row: Days Remaining and Day with Date */}
        <View style={styles.examMiddleRow}>
          <Text style={[
            styles.examDaysRemaining,
            examStatus.status === 'passed' && styles.examPassedText,
            examStatus.status === 'now' && styles.examNowText,
            examStatus.status === 'today' && styles.examTodayText,
            examStatus.status === 'upcoming' && styles.examUpcomingText
          ]}>
            {examStatus.status === 'passed'
              ? 'Passed'
              : examStatus.status === 'now'
                ? 'Now'
                : examStatus.status === 'today'
                  ? 'Today'
                  : examStatus.days === 1
                    ? 'Tomorrow'
                    : `${examStatus.days} days left`
            }
          </Text>
          <View style={styles.examDayContainer}>
            <Text style={[
              styles.examDay,
              examStatus.status === 'passed' && styles.examTextPassed
            ]}>
              {examDay}
            </Text>
            <Text style={[
              styles.examDate,
              examStatus.status === 'passed' && styles.examTextPassed
            ]}>
              {formatDateDisplay(examDate)}
            </Text>
          </View>
        </View>

        {/* Bottom Row: Time, Duration, Location */}
        <View style={styles.examBottomRow}>
          <View style={styles.examTimeSection}>
            <Text style={[
              styles.examTimeLabel,
              examStatus.status === 'passed' && styles.examTextPassed
            ]}>
              Start time
            </Text>
            <Text style={[
              styles.examTimeValue,
              examStatus.status === 'passed' && styles.examTextPassed
            ]}>
              {timeInfo.startTime}
            </Text>
          </View>

          <View style={styles.examDurationSection}>
            <Text style={[
              styles.examTimeLabel,
              examStatus.status === 'passed' && styles.examTextPassed
            ]}>
              Duration
            </Text>
            <Text style={[
              styles.examTimeValue,
              examStatus.status === 'passed' && styles.examTextPassed
            ]}>
              {timeInfo.duration}
            </Text>
          </View>

          <View style={styles.examLocationSection}>
            <Text style={[
              styles.examLocationLabel,
              examStatus.status === 'passed' && styles.examTextPassed
            ]}>
              {examHall}
            </Text>
            <Text style={[
              styles.examSeat,
              examStatus.status === 'passed' && styles.examTextPassed
            ]}>
              Seat {examSeat}
            </Text>
          </View>
        </View>
      </View>
    </Animated.View>
  );
};

// Ghost Loading Component for Exams
const ExamGhostCard = ({ index, theme, safeCurrentThemeName }) => {
  const shimmerAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerAnim, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    shimmerAnimation.start();

    return () => shimmerAnimation.stop();
  }, []);

  const shimmerOpacity = shimmerAnim.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  const ghostStyles = {
    examCard: {
      backgroundColor: theme.colors.surfaceSecondary,
      borderRadius: 12,
      padding: 16,
      marginBottom: 12,
      borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 1,
      borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : theme.colors.border,
    },
    ghostLine: {
      backgroundColor: theme.colors.border,
      borderRadius: 4,
    },
    ghostTopRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    ghostMiddleRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: 12,
    },
    ghostBottomRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-start',
    },
  };

  return (
    <View style={ghostStyles.examCard}>
      {/* Top Row: Course Name and Course Code Badge */}
      <View style={ghostStyles.ghostTopRow}>
        <Animated.View style={[
          ghostStyles.ghostLine,
          { width: '60%', height: 18, opacity: shimmerOpacity }
        ]} />
        <Animated.View style={[
          ghostStyles.ghostLine,
          { width: 80, height: 28, borderRadius: 16, opacity: shimmerOpacity }
        ]} />
      </View>

      {/* Middle Row: Days Remaining and Day with Date */}
      <View style={ghostStyles.ghostMiddleRow}>
        <Animated.View style={[
          ghostStyles.ghostLine,
          { width: '40%', height: 16, opacity: shimmerOpacity }
        ]} />
        <View style={{ alignItems: 'flex-end' }}>
          <Animated.View style={[
            ghostStyles.ghostLine,
            { width: 60, height: 14, marginBottom: 4, opacity: shimmerOpacity }
          ]} />
          <Animated.View style={[
            ghostStyles.ghostLine,
            { width: 40, height: 12, opacity: shimmerOpacity }
          ]} />
        </View>
      </View>

      {/* Bottom Row: Time, Duration, Location */}
      <View style={ghostStyles.ghostBottomRow}>
        <View style={{ flex: 1, alignItems: 'flex-start' }}>
          <Animated.View style={[
            ghostStyles.ghostLine,
            { width: 50, height: 12, marginBottom: 4, opacity: shimmerOpacity }
          ]} />
          <Animated.View style={[
            ghostStyles.ghostLine,
            { width: 60, height: 14, opacity: shimmerOpacity }
          ]} />
        </View>

        <View style={{ flex: 1, alignItems: 'center' }}>
          <Animated.View style={[
            ghostStyles.ghostLine,
            { width: 50, height: 12, marginBottom: 4, opacity: shimmerOpacity }
          ]} />
          <Animated.View style={[
            ghostStyles.ghostLine,
            { width: 40, height: 14, opacity: shimmerOpacity }
          ]} />
        </View>

        <View style={{ flex: 1, alignItems: 'flex-end' }}>
          <Animated.View style={[
            ghostStyles.ghostLine,
            { width: 70, height: 14, marginBottom: 2, opacity: shimmerOpacity }
          ]} />
          <Animated.View style={[
            ghostStyles.ghostLine,
            { width: 50, height: 12, opacity: shimmerOpacity }
          ]} />
        </View>
      </View>
    </View>
  );
};

const ExamsScreen = ({ navigation }) => {
  // Theme context
  const { theme, currentThemeName } = useTheme();

  // Fallback for currentThemeName to prevent undefined errors
  const safeCurrentThemeName = currentThemeName || 'dark';


  const [credentials, setCredentials] = useState(null);
  const [examsUrl, setExamsUrl] = useState('');
  const [isSidebarVisible, setIsSidebarVisible] = useState(false);
  const [sidebarAnim] = useState(new Animated.Value(-300));
  const [examsData, setExamsData] = useState([]);
  const [isLoadingExams, setIsLoadingExams] = useState(false);
  const [refreshRotation] = useState(new Animated.Value(0)); // For rotating refresh arrow
  const [hasCachedData, setHasCachedData] = useState(false);
  const [fetchCompleted, setFetchCompleted] = useState(false); // Track if initial fetch is complete
  const webViewRef = useRef(null);

  // Refs for tracking background operations and component mount state
  const isMountedRef = useRef(true);
  const activeTimeoutsRef = useRef(new Set());

  // Comprehensive function to kill all background operations
  const killAllBackgroundOperations = async () => {
    console.log('🛑 ExamsScreen: Killing all background operations...');

    // Mark component as unmounted to prevent state updates
    isMountedRef.current = false;

    // Clear all active timeouts
    activeTimeoutsRef.current.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    activeTimeoutsRef.current.clear();

    // Stop all animations
    refreshRotation.stopAnimation();
    sidebarAnim.stopAnimation();

    // Reset all loading states to prevent cache corruption
    setIsLoadingExams(false);
    setIsRefreshing(false);

    // Dispose of WebView
    await disposeWebView(webViewRef, 'exams-webview');

    console.log('✅ ExamsScreen: All background operations killed');
  };

  // Safe state setter that checks if component is still mounted
  const safeSetState = (setter, value, stateName) => {
    if (isMountedRef.current) {
      setter(value);
    } else {
      console.log(`⚠️ ExamsScreen: Prevented ${stateName} state update after unmount`);
    }
  };

  // Safe timeout wrapper that tracks timeouts for cleanup
  const safeSetTimeout = (callback, delay) => {
    const timeoutId = setTimeout(() => {
      activeTimeoutsRef.current.delete(timeoutId);
      if (isMountedRef.current) {
        callback();
      }
    }, delay);
    activeTimeoutsRef.current.add(timeoutId);
    return timeoutId;
  };

  // Removed dummy data - using real exam data from WebView

  useEffect(() => {
    initializeScreenWithCache();

    // Cleanup function
    return () => {
      console.log('🧹 ExamsScreen: Component unmounting - cleaning up...');
      killAllBackgroundOperations();
    };
  }, []);

  // Handle navigation focus/blur - kill background operations when losing focus
  useFocusEffect(
    useCallback(() => {
      console.log('🔄 ExamsScreen: Screen focused');

      // Mark component as mounted when focused
      isMountedRef.current = true;

      // Return cleanup function that runs when screen loses focus
      return () => {
        console.log('🔄 ExamsScreen: Screen losing focus - killing background operations...');
        killAllBackgroundOperations();
      };
    }, [])
  );

  // Kill background operations when navigating away completely
  useEffect(() => {
    const unsubscribe = navigation.addListener('beforeRemove', async () => {
      console.log('🧹 ExamsScreen: Screen unmounting - Killing all background operations...');
      await killAllBackgroundOperations();
    });

    return unsubscribe;
  }, [navigation]);

  // Initialize screen with cache-first strategy
  const initializeScreenWithCache = async () => {
    console.log('🚀 Initializing ExamsScreen with cache-first strategy');

    // Check for demo mode first
    const isDemo = await isDemoMode();
    if (isDemo) {
      console.log('🎭 Demo mode detected - loading demo exam data');
      await simulateLoading(800); // Simulate loading delay
      
      // Load demo exam data
      const demoExams = getDemoExamsData();
      const sortedDemoExams = sortExamsByDate(demoExams);
      
      setExamsData(sortedDemoExams);
      setHasCachedData(true);
      setFetchCompleted(true);
      
      console.log('✅ Demo exam data loaded successfully');
      return;
    }

    // Step 1: Try to load from cache first
    const hasCachedData = await loadFromCache();

    if (hasCachedData) {
      console.log('✅ Using cached exam data');
      setHasCachedData(true);
    } else {
      console.log('📭 No cached data, will load fresh data');
      setHasCachedData(false);
    }

    // Step 2: Load fresh data from WebView
    await loadCredentialsAndSetupExams(false);
  };

  const openSidebar = () => {
    setIsSidebarVisible(true);
    Animated.timing(sidebarAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const closeSidebar = () => {
    Animated.timing(sidebarAnim, {
      toValue: -300,
      duration: 300,
      useNativeDriver: true,
    }).start(() => {
      setIsSidebarVisible(false);
    });
  };

  // Swipe gesture handler for opening/closing sidebar - Enhanced for Android
  const swipeGestureHandler = PanResponder.create({
    onStartShouldSetPanResponder: () => false, // Don't capture immediately
    onStartShouldSetPanResponderCapture: () => false, // Don't capture on start
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to significant horizontal swipes
      const { dx, dy } = gestureState;

      // Only capture if it's a clear horizontal swipe with significant movement
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
      // Only capture significant horizontal swipes to prevent Android system gestures
      const { dx, dy } = gestureState;
      return Math.abs(dx) > Math.abs(dy) && Math.abs(dx) > 30;
    },
    onPanResponderGrant: () => {
      // Gesture has been granted - prevent other handlers
      return true;
    },
    onPanResponderMove: () => {
      // Optional: Add visual feedback during swipe
    },
    onPanResponderRelease: (evt, gestureState) => {
      const { dx, dy } = gestureState;
      const isHorizontalSwipe = Math.abs(dx) > Math.abs(dy);
      const swipeDistance = Math.abs(dx);

      if (isHorizontalSwipe && swipeDistance > 100) {
        if (dx > 0) {
          // Swipe right - open sidebar
          openSidebar();
        } else {
          // Swipe left - close sidebar if it's open
          if (isSidebarVisible) {
            closeSidebar();
          }
        }
      }
    },
    onPanResponderTerminationRequest: () => false, // Don't allow termination once we have it
    onShouldBlockNativeResponder: () => true, // Block native responders only when we have the gesture
  });

  // Cache management functions
  const CACHE_KEY = 'exams_cache';
  const CACHE_TIMESTAMP_KEY = 'exams_cache_timestamp';

  const saveToCache = async (data) => {
    try {
      const cacheData = {
        examsData: data,
        timestamp: Date.now()
      };
      await AsyncStorage.setItem(CACHE_KEY, JSON.stringify(cacheData));
      await AsyncStorage.setItem(CACHE_TIMESTAMP_KEY, Date.now().toString());
      console.log('📦 Exams data cached successfully');
    } catch (error) {
      console.log('❌ Error saving exams to cache:', error);
    }
  };

  const loadFromCache = async () => {
    try {
      const cachedData = await AsyncStorage.getItem(CACHE_KEY);
      if (cachedData) {
        const parsedData = JSON.parse(cachedData);
        console.log('📦 Loading cached exams data');

        // Set cached data to state
        setExamsData(parsedData.examsData || []);
        setHasCachedData(true);
        setFetchCompleted(true); // Mark as completed since we have cached data

        return true; // Cache loaded successfully
      }
      return false; // No cache found
    } catch (error) {
      console.log('❌ Error loading exams from cache:', error);
      return false;
    }
  };

  const clearCache = async () => {
    try {
      await AsyncStorage.removeItem(CACHE_KEY);
      await AsyncStorage.removeItem(CACHE_TIMESTAMP_KEY);
      console.log('🗑️ Exams cache cleared');
    } catch (error) {
      console.log('❌ Error clearing exams cache:', error);
    }
  };

  // Rotation animation functions
  const startRotationAnimation = () => {
    refreshRotation.setValue(0);
    Animated.loop(
      Animated.timing(refreshRotation, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: true,
      })
    ).start();
  };

  const stopRotationAnimation = () => {
    Animated.timing(refreshRotation, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  };

  const handleRefresh = async () => {
    console.log('🔄 Refresh button pressed - reloading exam data');

    // Check if we're in demo mode
    const isDemo = await isDemoMode();
    if (isDemo) {
      console.log('🎭 Demo mode - simulating refresh');
      
      // Start loading state and rotation animation
      setIsLoadingExams(true);
      setFetchCompleted(false);
      startRotationAnimation();
      
      // Simulate loading delay
      await simulateLoading(1500);
      
      // Reload demo exam data
      const demoExams = getDemoExamsData();
      const sortedDemoExams = sortExamsByDate(demoExams);
      
      setExamsData(sortedDemoExams);
      setIsLoadingExams(false);
      setFetchCompleted(true);
      stopRotationAnimation();
      
      console.log('🎭 Demo refresh complete');
      return;
    }

    // Clear existing cache before refreshing
    await clearCache();

    // Start loading state and rotation animation
    setIsLoadingExams(true);
    setFetchCompleted(false); // Reset fetch completion status
    startRotationAnimation();

    try {
      // Load fresh exam data from WebView
      await loadCredentialsAndSetupExams(true); // true indicates this is a refresh

      // Wait a moment for the URL to be set, then force WebView reload (optimized)
      safeSetTimeout(() => {
        if (webViewRef.current) {
          console.log('🔄 Forcing WebView reload for refresh...');
          webViewRef.current.reload();
        }
      }, 300); // Optimized from 500ms to 300ms

      // Safety timeout to stop loading if something goes wrong (optimized)
      safeSetTimeout(() => {
        if (isLoadingExams) {
          console.log('⚠️ Refresh timeout - stopping loading state');
          safeSetState(setIsLoadingExams, false, 'isLoadingExams');
          safeSetState(setFetchCompleted, true, 'fetchCompleted');
          stopRotationAnimation();
        }
      }, 8000); // Optimized from 15 seconds to 8 seconds

    } catch (error) {
      console.error('❌ Error during refresh:', error);
      setIsLoadingExams(false);
      setFetchCompleted(true);
      stopRotationAnimation();
    }
  };





  // Sort exams: upcoming first (by date), then passed (by date)
  const sortExamsByDate = (exams) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Parse date helper
    const parseDate = (dateStr) => {
      try {
        // Primary approach: handle DD/MM/YYYY format first
        const parts = dateStr.split('/');
        if (parts.length === 3) {
          const date = new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
          if (!isNaN(date.getTime())) {
            return date;
          }
        }

        // Secondary approach: try to parse as-is
        let date = new Date(dateStr);
        if (!isNaN(date.getTime())) {
          return date;
        }

        // Fallback approach: Handle format like "2 - August - 2025"
        if (dateStr.includes(' - ')) {
          const parts = dateStr.split(' - ');
          if (parts.length >= 3) {
            const day = parts[0].trim();
            const monthName = parts[1].trim();
            const year = parts[2].trim();

            // Convert month name to number
            const monthMap = {
              'January': 0, 'Jan': 0,
              'February': 1, 'Feb': 1,
              'March': 2, 'Mar': 2,
              'April': 3, 'Apr': 3,
              'May': 4,
              'June': 5, 'Jun': 5,
              'July': 6, 'Jul': 6,
              'August': 7, 'Aug': 7,
              'September': 8, 'Sep': 8,
              'October': 9, 'Oct': 9,
              'November': 10, 'Nov': 10,
              'December': 11, 'Dec': 11
            };

            const monthNum = monthMap[monthName];
            if (monthNum !== undefined) {
              // Create Date object with proper month number (0-based)
              date = new Date(parseInt(year), monthNum, parseInt(day));

              if (!isNaN(date.getTime())) {
                return date;
              }
            }
          }
        }

        return new Date(0);
      } catch (error) {
        return new Date(0);
      }
    };

    // Separate upcoming and passed exams
    const upcomingExams = [];
    const passedExams = [];

    exams.forEach(exam => {
      const examDate = parseDate(exam.Date);
      if (examDate.getTime() >= today.getTime()) {
        upcomingExams.push(exam);
      } else {
        passedExams.push(exam);
      }
    });

    // Sort upcoming exams by date (earliest first)
    upcomingExams.sort((a, b) => {
      const dateA = parseDate(a.Date);
      const dateB = parseDate(b.Date);
      return dateA.getTime() - dateB.getTime();
    });

    // Sort passed exams by date (most recent first)
    passedExams.sort((a, b) => {
      const dateA = parseDate(a.Date);
      const dateB = parseDate(b.Date);
      return dateB.getTime() - dateA.getTime();
    });

    // Return upcoming first, then passed
    return [...upcomingExams, ...passedExams];
  };

  const loadCredentialsAndSetupExams = async (isBackgroundRefresh = false) => {
    try {
      console.log('🔄 Loading credentials for exams...');

      // Clear WebView session before setting up exams (only if not background refresh)
      if (!isBackgroundRefresh) {
        await clearWebViewSession();
      }

      // Get stored credentials from localStorage
      const storedUsername = await AsyncStorage.getItem('guc_username');
      const storedPassword = await AsyncStorage.getItem('guc_password');

      if (!storedUsername || !storedPassword) {
        console.log('❌ No stored credentials found');
        if (!isBackgroundRefresh) {
          // Alert.alert(
          //   'Authentication Required',
          //   'Please login first to access exams.',
          //   [
          //     {
          //       text: 'Go to Login',
          //       onPress: () => navigation.navigate('Login')
          //     }
          //   ]
          // );
        }
        // Removed setIsUpdatingData - using refresh button loading instead
        return;
      }

      console.log('✅ Credentials loaded, setting up exams URL...');
      setCredentials({ username: storedUsername, password: storedPassword });

      // Create URL with embedded credentials - properly encode username and password
      const encodedUsername = encodeURIComponent(storedUsername);
      const encodedPassword = encodeURIComponent(storedPassword);
      const urlWithCredentials = `https://${encodedUsername}:${encodedPassword}@apps.guc.edu.eg/student_ext/Exam/ViewExamSeat_01.aspx`;
      setExamsUrl(urlWithCredentials);

      console.log('🎯 Exams URL prepared');

    } catch (error) {
      console.error('❌ Error loading credentials:', error);
      if (!isBackgroundRefresh) {
        // Alert.alert(
        //   'Error',
        //   'Failed to load credentials. Please try again.',
        //   [
        //     {
        //       text: 'Go Back',
        //       onPress: () => navigation.goBack()
        //     }
        //   ]
        // );
      }
      // Removed setIsUpdatingData - using refresh button loading instead
    }
  };

  const handleWebViewLoad = () => {
    console.log('📚 Exams page loaded successfully');

    // Start loading exams data (only show loading if no cached data or if we're refreshing)
    if (!hasCachedData || isLoadingExams) {
      setIsLoadingExams(true);
      startRotationAnimation();
    }

    // Add a delay to ensure the page is fully loaded (optimized)
    safeSetTimeout(() => {
      extractExamData();
    }, 1000); // Optimized from 2 seconds to 1 second
  };

  const extractExamData = () => {
    if (webViewRef.current) {
      const jsCode = `
        (function() {
          try {
            console.log('🔍 Looking for exam table...');
            
            // Function to parse table to objects
            function parseTableToObjects(tableElement) {
              const rows = tableElement.querySelectorAll('tr');
              const headers = rows[0].querySelectorAll('td');
              const dataRows = Array.from(rows).slice(1); // Exclude header row

              const objectsArray = dataRows.map((row, index) => {
                const cols = row.querySelectorAll('td');
                const examData = {
                  Course: cols[0] ? cols[0].textContent.trim() : '',
                  Day: cols[1] ? cols[1].textContent.trim() : '',
                  Date: cols[2] ? cols[2].textContent.trim() : '',
                  StartTime: cols[3] ? cols[3].textContent.trim() : '',
                  EndTime: cols[4] ? cols[4].textContent.trim() : '',
                  Hall: cols[5] ? cols[5].textContent.trim() : '',
                  Seat: cols[6] ? cols[6].textContent.trim() : '',
                  ExamType: cols[7] ? cols[7].textContent.trim() : ''
                };

                console.log('Exam row ' + index + ':', examData);
                return examData;
              });

              return objectsArray;
            }

            const tableElement = document.querySelector('tbody');
            if (tableElement) {
              console.log('✅ Table found, extracting data...');
              const examsArray = parseTableToObjects(tableElement);
              console.log('📊 Exams extracted:', examsArray);
              
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'exams_data_extracted',
                examsData: examsArray,
                totalExams: examsArray.length
              }));
            } else {
              console.log('❌ Table not found');
              window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'exams_error',
                error: 'Table element not found'
              }));
            }
          } catch (error) {
            console.log('❌ Error extracting exams:', error);
            window.ReactNativeWebView.postMessage(JSON.stringify({
              type: 'exams_error',
              error: error.message
            }));
          }
        })();
      `;

      webViewRef.current.injectJavaScript(jsCode);
    }
  };

  const handleWebViewMessage = (event) => {
    try {
      const data = JSON.parse(event.nativeEvent.data);
      console.log('📨 WebView message received:', data);

      if (data.type === 'exams_data_extracted') {
        console.log('🎯 Exams data extracted!');
        console.log('📊 Total exams found:', data.totalExams);
        console.log('📊 Exams data:', data.examsData);

        // Sort exams by date
        const sortedExams = sortExamsByDate(data.examsData);

        console.log('📊 Final exams data:', sortedExams);
        safeSetState(setExamsData, sortedExams, 'examsData');
        safeSetState(setIsLoadingExams, false, 'isLoadingExams');
        safeSetState(setFetchCompleted, true, 'fetchCompleted'); // Mark fetch as completed
        stopRotationAnimation();

        // Save to cache when data is successfully extracted
        saveToCache(sortedExams);
      } else if (data.type === 'exams_error') {
        console.log('❌ Exams extraction error:', data.error);
        safeSetState(setIsLoadingExams, false, 'isLoadingExams');
        safeSetState(setFetchCompleted, true, 'fetchCompleted'); // Mark fetch as completed even on error
        stopRotationAnimation();
      }
    } catch (error) {
      console.log('❌ Error processing WebView message:', error);
      safeSetState(setIsLoadingExams, false, 'isLoadingExams');
      safeSetState(setFetchCompleted, true, 'fetchCompleted'); // Mark fetch as completed on error
      stopRotationAnimation();
    }
  };

  const handleWebViewError = (syntheticEvent) => {
    const { nativeEvent } = syntheticEvent;
    console.error('❌ Exams WebView error:', nativeEvent);
    
    // Alert.alert(
    //   'Loading Error',
    //   'Failed to load exams page. Please check your connection and try again.',
    //   [
    //     {
    //       text: 'Retry',
    //       onPress: () => {
    //         setFetchCompleted(false);
    //         loadCredentialsAndSetupExams();
    //       }
    //     },
    //     {
    //       text: 'Go Back',
    //       onPress: () => navigation.goBack()
    //     }
    //   ]
    // );
  };

  // Generate styles based on current theme
  const styles = createStyles(theme, safeCurrentThemeName);

  return (
    <View style={{ flex: 1 }} {...swipeGestureHandler.panHandlers}>
      <SafeAreaView style={styles.container}>
      {/* Sidebar Button */}
      <View style={styles.sidebarButtonContainer}>
        <TouchableOpacity style={styles.sidebarButton} onPress={openSidebar}>
          <HamburgerIcon size={20} color={theme.colors.primary} strokeWidth={3} />
        </TouchableOpacity>
      </View>

      {/* Exams Title */}
      <View style={styles.examsTitleContainer}>
        <Text style={styles.examsTitle}>Exams</Text>
      </View>

      {/* Refresh Button */}
      <View style={styles.refreshButtonContainer}>
        <TouchableOpacity
          style={[
            styles.refreshButton,
            isLoadingExams && styles.refreshButtonLoading
          ]}
          onPress={handleRefresh}
          disabled={isLoadingExams}
        >
          <Animated.View
            style={{
              transform: [{
                rotate: refreshRotation.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0deg', '360deg']
                })
              }]
            }}
          >
            <RefreshIcon
              size={24}
              color={isLoadingExams ? theme.colors.textSecondary : '#f1c40f'}
            />
          </Animated.View>
        </TouchableOpacity>
      </View>

      {/* Update Indicator removed - using refresh button loading state instead */}

      {/* Exams Content */}
      <View style={styles.examsContent}>
        {(!fetchCompleted && !hasCachedData) ? (
          // Show ghost loading when no cached data and fetch not completed
          <ScrollView style={styles.examsList} showsVerticalScrollIndicator={false}>
            {[1, 2, 3, 4, 5].map((index) => (
              <ExamGhostCard
                key={index}
                index={index}
                theme={theme}
                safeCurrentThemeName={safeCurrentThemeName}
              />
            ))}
          </ScrollView>
        ) : examsData.length > 0 ? (
          <ScrollView style={styles.examsList} showsVerticalScrollIndicator={false}>
            {examsData.map((exam, index) => (
              <AnimatedExamCard
                key={index}
                exam={exam}
                index={index}
                styles={styles}
                theme={theme}
                safeCurrentThemeName={safeCurrentThemeName}
              />
            ))}
          </ScrollView>
        ) : fetchCompleted ? (
          // Only show "No exams found" after fetch is completed
          <View style={styles.noExamsContainer}>
            <Text style={styles.noExamsText}>No exams found</Text>
          </View>
        ) : null}
      </View>

      {/* Hidden WebView for data extraction */}
      <View style={styles.hiddenWebView}>
        <WebView
          ref={webViewRef}
          source={{ uri: examsUrl }}
          onLoad={handleWebViewLoad}
          onMessage={handleWebViewMessage}
          onError={handleWebViewError}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          mixedContentMode="compatibility"
          cacheEnabled={false}
          incognito={true}
        />
      </View>

      {/* Sidebar Component */}
      <Sidebar
        isVisible={isSidebarVisible}
        onClose={closeSidebar}
        sidebarAnim={sidebarAnim}
        navigation={navigation}
        currentScreen="Exams"
      />
    </SafeAreaView>
    </View>
  );
};

// Create styles function that uses theme
const createStyles = (theme, safeCurrentThemeName) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  sidebarButtonContainer: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 1000,
  },
  sidebarButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
  },
  examsTitleContainer: {
    position: 'absolute',
    top: 55, // Raised higher
    left: 0,
    right: 0,
    alignItems: 'center',
    zIndex: 999,
  },
  examsTitle: {
    fontSize: 30, // Increased font size slightly
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  refreshButtonContainer: {
    position: 'absolute',
    top: 50,
    right: 20,
    zIndex: 1000,
  },
  refreshButton: {
    backgroundColor: theme.colors.surface,
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: theme.colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 0,
    borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : 'transparent',
  },
  refreshButtonLoading: {
    backgroundColor: theme.colors.surfaceSecondary,
    opacity: 0.7,
  },
  examsContent: {
    flex: 1,
    paddingTop: 100, // Reduced from 120 to bring exams closer to header
    paddingHorizontal: 20,
  },
  examsList: {
    flex: 1,
  },
  examCard: {
    backgroundColor: theme.colors.surfaceSecondary,
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 1,
    borderColor: theme.colors.border, // Default border, will be overridden by specific states
  },
  examCardPassed: {
    backgroundColor: theme.colors.surface,
    opacity: 0.6,
    borderColor: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.success, // Green for passed
    borderWidth: safeCurrentThemeName === 'colorful' ? 3 : 2,
  },
  examCardToday: {
    borderColor: '#FF4444', // Lighter red for today's exams
    borderWidth: 2,
  },
  examCardNow: {
    borderColor: '#FF4444', // Lighter red for current exams
    borderWidth: 2,
    backgroundColor: theme.colors.surfaceSecondary,
  },
  examCardUpcoming: {
    borderColor: '#FFA500', // Orange for upcoming exams (not today)
    borderWidth: 2,
  },
  examTopRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  examCourse: {
    fontSize: 18,
    fontWeight: '600',
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    flex: 1,
    marginRight: 8, // Add space between course name and code badge
  },
  examCodeBadge: {
    backgroundColor: safeCurrentThemeName === 'colorful' ? '#4A5568' : '#4A5568', // Darker grey background for prominence
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 2, // Add shadow on Android
    shadowColor: '#000', // Add shadow on iOS
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    alignSelf: 'flex-start',
    marginLeft: 8, // Push it more to the right
  },
  examCodeBadgePassed: {
    backgroundColor: '#6B7280', // Slightly lighter for passed exams
    opacity: 0.8,
  },
  examCodeText: {
    fontSize: 12, // Smaller font size
    fontWeight: '600', // Slightly less bold
    color: '#FFFFFF', // Always white text for contrast
    letterSpacing: 0.3, // Reduced letter spacing
  },
  examCodeTextPassed: {
    color: '#E5E7EB', // Light grey for passed exams
  },
  examMiddleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  examDaysRemaining: {
    fontSize: 16,
    fontWeight: '600',
    color: safeCurrentThemeName === 'colorful' ? '#FFD700' : theme.colors.error, // Yellow for days left in pink mode
  },
  examDayContainer: {
    alignItems: 'flex-end',
  },
  examDay: {
    fontSize: 14,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontWeight: '500',
  },
  examDate: {
    fontSize: 12,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textTertiary,
    fontWeight: '400',
    marginTop: 2,
  },
  examBottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  examTimeSection: {
    flex: 1,
    alignItems: 'flex-start',
  },
  examDurationSection: {
    flex: 1,
    alignItems: 'center',
  },
  examLocationSection: {
    flex: 1,
    alignItems: 'flex-end',
  },
  examTimeLabel: {
    fontSize: 12,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontWeight: '500',
    marginBottom: 4,
  },
  examTimeValue: {
    fontSize: 14,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    fontWeight: '600',
  },
  examLocationLabel: {
    fontSize: 14,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
    fontWeight: '600',
    textAlign: 'right',
  },
  examSeat: {
    fontSize: 12,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontWeight: '500',
    textAlign: 'right',
    marginTop: 2,
  },
  examTextPassed: {
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textTertiary,
  },
  examPassedText: {
    color: safeCurrentThemeName === 'colorful' ? '#00CED1' : theme.colors.success, // Turquoise for "Passed" in pink mode
    fontWeight: '600',
  },
  examNowText: {
    color: '#FF4444', // Lighter red for "Now" status
    fontWeight: '700',
    fontSize: 18, // Increased from 16
  },
  examTodayText: {
    color: '#FF4444', // Lighter red for "Today" status
    fontWeight: '600',
    fontSize: 16, // Increased from 14
  },
  examUpcomingText: {
    color: '#FFA500', // Orange for upcoming exams (Tomorrow, X days left)
    fontWeight: '600',
    fontSize: 16, // Increased from 14
  },
  noExamsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noExamsText: {
    fontSize: 18,
    color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.textSecondary,
    fontWeight: '500',
  },
  hiddenWebView: {
    position: 'absolute',
    top: -1000,
    left: -1000,
    width: 1,
    height: 1,
    opacity: 0,
  },
});

export default ExamsScreen;