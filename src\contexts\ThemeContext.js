import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform, Animated } from 'react-native';

// Theme definitions
const themes = {
  dark: {
    name: 'dark',
    colors: {
      // Background colors
      background: '#2C2C2C', // Changed from very dark black to dark grey
      surface: '#3A3A3A', // Lightened from #1A1A1A
      surfaceSecondary: '#4A4A4A', // Lightened from #2A2A2A

      // Text colors
      text: '#FFFFFF',
      textSecondary: '#B0B0B0', // Lightened from #9CA3AF
      textTertiary: '#D0D0D0', // Lightened from #CCCCCC

      // Accent colors
      primary: '#EAB308', // GUC Yellow
      primaryText: '#2C2C2C', // Updated to match new background

      // Status colors
      success: '#10B981',
      error: '#EF4444',
      warning: '#F59E0B',
      info: '#3B82F6',

      // Border colors
      border: '#555555', // Lightened from #333333
      borderLight: '#666666', // Lightened from #444444

      // Modal and overlay colors
      modalOverlay: 'rgba(44, 44, 44, 0.5)', // Updated to match new background
      shadow: '#1A1A1A', // Lightened from #000

      // Notification bell colors
      bellBackground: '#DC2626', // Red bell background
      bellText: '#FFFFFF', // White text on red background
    }
  },
  
  light: {
    name: 'light',
    colors: {
      // Background colors
      background: '#FFFFFF',
      surface: '#F8F9FA',
      surfaceSecondary: '#E9ECEF',

      // Text colors
      text: '#212529',
      textSecondary: '#6C757D',
      textTertiary: '#495057',

      // Accent colors
      primary: '#EAB308', // Keep GUC Yellow
      primaryText: '#FFFFFF',

      // Status colors
      success: '#198754',
      error: '#DC3545',
      warning: '#FD7E14',
      info: '#0D6EFD',

      // Border colors
      border: '#DEE2E6',
      borderLight: '#E9ECEF',

      // Modal and overlay colors
      modalOverlay: 'rgba(0, 0, 0, 0.3)',
      shadow: '#000',

      // Notification bell colors
      bellBackground: '#DC2626', // Red bell background
      bellText: '#FFFFFF', // White text on red background
    }
  },
  
  colorful: {
    name: 'colorful',
    colors: {
      // Background colors - Pink base
      background: '#FF69B4', // Hot Pink background
      surface: '#C71585', // Darker pink for notifications bar
      surfaceSecondary: '#C71585',

      // Text colors - All white for better readability
      text: '#FFFFFF', // White text color
      textSecondary: '#FFFFFF', // White secondary text
      textTertiary: '#FFFFFF', // White tertiary text

      // Accent colors - Yellow primary
      primary: '#FFD700', // Gold/Yellow primary
      primaryText: '#FFFFFF', // White text on yellow for better contrast

      // Status colors - Bright and vibrant
      success: '#FFD700', // Gold/Yellow
      error: '#FF1493', // Deep Pink
      warning: '#FF6347', // Tomato
      info: '#00CED1', // Dark Turquoise

      // Border colors - Turquoise borders for pink theme
      border: '#00CED1', // Dark Turquoise border
      borderLight: '#40E0D0', // Turquoise border

      // Modal and overlay colors
      modalOverlay: 'rgba(199, 21, 133, 0.4)', // Pink overlay to match theme
      shadow: '#FFD700',

      // Notification bell colors
      bellBackground: '#00CED1', // Cyan bell background for colorful theme
      bellText: '#000000', // Black text on cyan background
    }
  },

  navy: {
    name: 'navy',
    colors: {
      // Background colors - Navy base with sophisticated depth
      background: '#1B2951', // Deep navy blue background
      surface: '#243B73', // Lighter navy for surfaces
      surfaceSecondary: '#2D4A8C', // Even lighter navy for secondary surfaces

      // Text colors - Crisp whites and light grays
      text: '#FFFFFF', // Pure white text
      textSecondary: '#E2E8F0', // Light gray secondary text
      textTertiary: '#CBD5E0', // Slightly darker gray tertiary text

      // Accent colors - Elegant red accents
      primary: '#DC2626', // Rich red primary color
      primaryText: '#FFFFFF', // White text on red

      // Status colors - Navy theme appropriate colors
      success: '#059669', // Emerald green
      error: '#DC2626', // Red (matches primary)
      warning: '#D97706', // Amber
      info: '#2563EB', // Blue

      // Border colors - Subtle red and navy borders
      border: '#DC2626', // Red borders for accent
      borderLight: '#475569', // Slate gray for subtle borders

      // Modal and overlay colors
      modalOverlay: 'rgba(27, 41, 81, 0.6)', // Navy overlay
      shadow: '#0F172A', // Very dark navy shadow

      // Notification bell colors
      bellBackground: '#DC2626', // Red bell background
      bellText: '#FFFFFF', // White text on red background
    }
  }
};

// Create context
const ThemeContext = createContext();

// Theme provider component
export const ThemeProvider = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState('dark');
  const [isLoading, setIsLoading] = useState(true);
  const [isTransitioning, setIsTransitioning] = useState(false);

  // Animation values for smooth theme transitions
  const transitionAnim = useRef(new Animated.Value(1)).current;
  const fadeAnim = useRef(new Animated.Value(1)).current;

  // Load saved theme on app start
  useEffect(() => {
    loadSavedTheme();
  }, []);



  const loadSavedTheme = async () => {
    try {
      const savedTheme = await AsyncStorage.getItem('app_theme');
      if (savedTheme && themes[savedTheme]) {
        setCurrentTheme(savedTheme);
      } else {
        // Fallback to dark theme if saved theme is invalid
        setCurrentTheme('dark');
      }
    } catch (error) {
      console.log('Error loading saved theme:', error);
      // Fallback to dark theme on error
      setCurrentTheme('dark');
    } finally {
      setIsLoading(false);
    }
  };

  const saveTheme = async (themeName) => {
    try {
      await AsyncStorage.setItem('app_theme', themeName);
    } catch (error) {
      console.log('Error saving theme:', error);
    }
  };



  // Animated theme switching with smooth fade transition
  const switchTheme = (themeName) => {
    if (themes[themeName] && themeName !== currentTheme) {
      console.log(`🎨 Starting theme transition: ${currentTheme} → ${themeName}`);
      setIsTransitioning(true);

      // Create a smooth fade-out, change theme, fade-in sequence
      Animated.sequence([
        // Fade out with slight scale down
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 0.3,
            duration: 200,
            useNativeDriver: true,
          }),
          Animated.timing(transitionAnim, {
            toValue: 0.95,
            duration: 200,
            useNativeDriver: true,
          }),
        ]),
        // Fade in with scale back up
        Animated.parallel([
          Animated.timing(fadeAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
          Animated.timing(transitionAnim, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }),
        ]),
      ]).start(() => {
        console.log(`✅ Theme transition completed: ${themeName}`);
        setIsTransitioning(false);
      });

      // Change theme in the middle of the fade (when opacity is lowest)
      setTimeout(() => {
        setCurrentTheme(themeName);
        saveTheme(themeName);
      }, 200);
    }
  };

  const switchToLight = () => switchTheme('light');
  const switchToDark = () => switchTheme('dark');
  const switchToColorful = () => {
    // Allow colorful theme on all platforms
    switchTheme('colorful');
  };
  const switchToNavy = () => {
    // Allow navy theme on all platforms
    switchTheme('navy');
  };

  const value = {
    theme: themes[currentTheme],
    currentThemeName: currentTheme,
    switchTheme,
    switchToLight,
    switchToDark,
    switchToColorful,
    switchToNavy,
    isLoading,
    isTransitioning,
    transitionAnim,
    fadeAnim,
    isAndroid: Platform.OS === 'android',
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use theme
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export default ThemeContext;
