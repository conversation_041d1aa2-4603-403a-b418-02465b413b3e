// Demo data for demonstration purposes
// This file contains realistic dummy data for all screens except Mail and CMS

import AsyncStorage from '@react-native-async-storage/async-storage';

export const DEMO_CREDENTIALS = {
  username: 'demo',
  password: 'myGUCdemo2025'
};

export const DEMO_STUDENT_DATA = {
  fullName: '<PERSON>',
  studentId: '58-0042',
  faculty: 'Photographic Engineering & Web Development'
};

// Enhanced notifications data with proper course codes, staff names, and realistic dates
export const DEMO_NOTIFICATIONS_DATA = [
  {
    id: 'demo-notif-1',
    title: 'Assignment 4 Submission Reminder',
    courseCode: 'CSEN602',
    staff: 'Dr. <PERSON>',
    date: '9/4/2025',
    content: 'This is a reminder that Assignment 4 for Operating Systems is due tomorrow at 11:59 PM. Please make sure to submit your work on time through the course portal. Late submissions will result in grade deductions.'
  },
  {
    id: 'demo-notif-2',
    title: 'Lab Quiz Announcement',
    courseCode: 'CSEN701',
    staff: 'Eng. <PERSON>',
    date: '9/4/2025',
    content: 'There will be a surprise lab quiz next Tuesday covering TCP/IP protocols and network troubleshooting. Please review your lab materials and be prepared.'
  },
  {
    id: 'demo-notif-3',
    title: 'Midterm Exam Schedule',
    courseCode: 'CSEN701',
    staff: 'Prof. Mohamed Ali',
    date: '9/3/2025',
    content: 'The midterm exam for Computer Networks will be held on September 10th, 2025 from 2:00 PM to 4:00 PM in Hall A. Please bring your student ID and calculator. No electronic devices are allowed except calculators.'
  },
  {
    id: 'demo-notif-4',
    title: 'Guest Lecture - Industry Expert',
    courseCode: 'CSEN703',
    staff: 'Dr. Omar Khaled',
    date: '9/3/2025',
    content: 'We are pleased to announce a guest lecture by Mr. Ahmed El-Sharkawy, Senior Software Architect at Microsoft, on September 8th at 2:00 PM in Hall C. Topic: Modern CPU Architecture and Performance Optimization.'
  },
  {
    id: 'demo-notif-5',
    title: 'Lab Session Cancelled',
    courseCode: 'MATH401',
    staff: 'Dr. Fatma Hassan',
    date: '9/2/2025',
    content: 'The lab session scheduled for Thursday September 5th has been cancelled due to a faculty meeting. The session will be rescheduled for next week. Please check the course portal for updates.'
  },
  {
    id: 'demo-notif-6',
    title: 'New Assignment Released',
    courseCode: 'ELCT601',
    staff: 'Dr. Nadia Mahmoud',
    date: '9/2/2025',
    content: 'Assignment 3: Digital Filter Design has been released on the course portal. This assignment involves designing and implementing FIR and IIR filters. Due date: September 16th, 2025.'
  },
  {
    id: 'demo-notif-7',
    title: 'Project Presentation Guidelines',
    courseCode: 'CSEN703',
    staff: 'Dr. Omar Khaled',
    date: '9/1/2025',
    content: 'Please find attached the guidelines for your final project presentations. Each team will have 15 minutes to present followed by 5 minutes for questions. Presentations will start on September 15th.'
  },
  {
    id: 'demo-notif-8',
    title: 'Makeup Exam Announcement',
    courseCode: 'MATH401',
    staff: 'Dr. Fatma Hassan',
    date: '9/1/2025',
    content: 'A makeup exam for Quiz 2 will be held on September 7th at 10:00 AM in Room C5.205. This is for students who missed the original quiz due to medical reasons with valid documentation.'
  },
  {
    id: 'demo-notif-9',
    title: 'Course Material Update',
    courseCode: 'ELCT601',
    staff: 'Dr. Nadia Mahmoud',
    date: '8/31/2025',
    content: 'New lecture slides for Digital Signal Processing have been uploaded to the course portal. Please review Chapter 7 before the next lecture. There will be a quiz on this material next week.'
  },
  {
    id: 'demo-notif-10',
    title: 'Study Group Formation',
    courseCode: 'CSEN602',
    staff: 'Eng. Sarah Mohamed',
    date: '8/31/2025',
    content: 'Students are encouraged to form study groups for the upcoming midterm exam. The TA office hours will be extended this week to accommodate group study sessions. Please coordinate with your classmates.'
  },
  {
    id: 'demo-notif-11',
    title: 'Office Hours Change',
    courseCode: 'CSEN602',
    staff: 'Dr. Sarah Ahmed',
    date: '8/30/2025',
    content: 'My office hours for this week have been moved from Tuesday 2-4 PM to Wednesday 3-5 PM. Please update your schedules accordingly. Office location remains the same (Room 301).'
  },
  {
    id: 'demo-notif-12',
    title: 'Research Paper Discussion',
    courseCode: 'CSEN701',
    staff: 'Prof. Mohamed Ali',
    date: '8/30/2025',
    content: 'Next week we will discuss the research paper "The Evolution of Internet Protocols" by Cerf & Kahn. Please read the paper beforehand as it will be part of your participation grade.'
  },
  {
    id: 'demo-notif-13',
    title: 'Group Formation Deadline',
    courseCode: 'CSEN701',
    staff: 'Prof. Mohamed Ali',
    date: '8/29/2025',
    content: 'Reminder: The deadline for forming project groups is September 8th. Each group should have 3-4 members. Please submit your group formation through the course portal.'
  },
  {
    id: 'demo-notif-14',
    title: 'Lab Equipment Maintenance',
    courseCode: 'ELCT601',
    staff: 'Eng. Yasmin Ahmed',
    date: '8/29/2025',
    content: 'Lab 3 will be closed for maintenance on Thursday, September 5th. All DSP lab sessions scheduled for that day will be moved to Lab 2. Please note the room change.'
  },
  {
    id: 'demo-notif-15',
    title: 'Extra Credit Opportunity',
    courseCode: 'MATH401',
    staff: 'Dr. Fatma Hassan',
    date: '8/28/2025',
    content: 'An extra credit assignment is now available on the course portal. This assignment is worth 5% of your final grade and is due on September 20th. Participation is optional but recommended.'
  },
  {
    id: 'demo-notif-16',
    title: 'Textbook Chapter Update',
    courseCode: 'CSEN703',
    staff: 'Dr. Omar Khaled',
    date: '8/28/2025',
    content: 'Please note that Chapter 8 readings have been updated in the course syllabus. The new chapter focuses on parallel processing architectures. Updated reading list is available on the course portal.'
  }
];

export const DEMO_GRADES_DATA = {
  'Course - CSEN602 Operating Systems': [
    {
      Name: 'Assignment 1',
      Grade: '18/20',
      staff: 'Dr. Ahmed Sallam'
    },
    {
      Name: 'Quiz 1',
      Grade: '9/10',
      staff: 'Dr. Ahmed Sallam'
    },
    {
      Name: 'Assignment 2',
      Grade: '15/20',
      staff: 'Dr. Ahmed Sallam'
    },
    {
      Name: 'Quiz 2',
      Grade: '10/10',
      staff: 'Dr. Ahmed Sallam'
    },
    {
      Name: 'Lab Project 1',
      Grade: '28/30',
      staff: 'Eng. Sarah Mohamed'
    },
    {
      Name: 'Lab Project 2',
      Grade: '0/30',
      staff: 'Eng. Sarah Mohamed'
    },
    {
      Name: 'Assignment 3',
      Grade: '12/20',
      staff: 'Dr. Ahmed Sallam'
    }
  ],
  'Course - CSEN701 Computer Networks': [
    {
      Name: 'Assignment 1',
      Grade: '17/20',
      staff: 'Dr. Mostafa Aref'
    },
    {
      Name: 'Quiz 1',
      Grade: '8/10',
      staff: 'Dr. Mostafa Aref'
    },
    {
      Name: 'Lab Assignment 1',
      Grade: '25/30',
      staff: 'Eng. Omar Hassan'
    },
    {
      Name: 'Assignment 2',
      Grade: '20/20',
      staff: 'Dr. Mostafa Aref'
    },
    {
      Name: 'Quiz 2',
      Grade: '6/10',
      staff: 'Dr. Mostafa Aref'
    },
    {
      Name: 'Lab Assignment 2',
      Grade: '30/30',
      staff: 'Eng. Omar Hassan'
    }
  ],
  'Course - MATH401 Probability & Statistics': [
    {
      Name: 'Assignment 1',
      Grade: '16/20',
      staff: 'Dr. Fatma Ali'
    },
    {
      Name: 'Quiz 1',
      Grade: '7/10',
      staff: 'Dr. Fatma Ali'
    },
    {
      Name: 'Assignment 2',
      Grade: '0/20',
      staff: 'Dr. Fatma Ali'
    },
    {
      Name: 'Quiz 2',
      Grade: '10/10',
      staff: 'Dr. Fatma Ali'
    },
    {
      Name: 'Assignment 3',
      Grade: '18/20',
      staff: 'Dr. Fatma Ali'
    }
  ],
  'Course - CSEN703 Advanced Computer Architecture': [
    {
      Name: 'Assignment 1',
      Grade: '19/20',
      staff: 'Dr. Khaled Shaban'
    },
    {
      Name: 'Quiz 1',
      Grade: '9/10',
      staff: 'Dr. Khaled Shaban'
    },
    {
      Name: 'Assignment 2',
      Grade: '14/20',
      staff: 'Dr. Khaled Shaban'
    },
    {
      Name: 'Quiz 2',
      Grade: '0/10',
      staff: 'Dr. Khaled Shaban'
    },
    {
      Name: 'Lab Project',
      Grade: '25/30',
      staff: 'Eng. Mona Hassan'
    }
  ],
  'Course - ELCT601 Digital Signal Processing': [
    {
      Name: 'Assignment 1',
      Grade: '16/20',
      staff: 'Dr. Amr Elshenawy'
    },
    {
      Name: 'Quiz 1',
      Grade: '8/10',
      staff: 'Dr. Amr Elshenawy'
    },
    {
      Name: 'Lab Report 1',
      Grade: '22/25',
      staff: 'Eng. Yasmin Ahmed'
    },
    {
      Name: 'Assignment 2',
      Grade: '10/20',
      staff: 'Dr. Amr Elshenawy'
    }
  ]
};

export const DEMO_MIDTERM_GRADES = {
  'Course - CSEN602 Operating Systems': 84,
  'Course - CSEN701 Computer Networks': 76,
  'Course - MATH401 Probability & Statistics': 70,
  'Course - CSEN703 Advanced Computer Architecture': 90,
  'Course - ELCT601 Digital Signal Processing': 80
};

export const DEMO_EXAMS_DATA = [
  {
    Course: 'CSEN602 - Operating Systems',
    Date: '2024-04-15',
    Day: 'Monday',
    StartTime: '09:00',
    EndTime: '12:00',
    Hall: 'C5.309',
    Seat: '15'
  },
  {
    Course: 'CSEN701 - Computer Networks',
    Date: '2024-04-17',
    Day: 'Wednesday',
    StartTime: '14:00',
    EndTime: '17:00',
    Hall: 'C5.205',
    Seat: '23'
  },
  {
    Course: 'MATH401 - Probability & Statistics',
    Date: '2024-04-20',
    Day: 'Saturday',
    StartTime: '09:00',
    EndTime: '12:00',
    Hall: 'C5.104',
    Seat: '8'
  },
  {
    Course: 'CSEN703 - Advanced Computer Architecture',
    Date: '2024-04-22',
    Day: 'Monday',
    StartTime: '14:00',
    EndTime: '17:00',
    Hall: 'C5.309',
    Seat: '12'
  }
];

export const DEMO_SCHEDULE_DATA = [
  {
    day: 'Saturday',
    slots: [
      { period: '1st', course: 'Free', location: '', type: 'Free', group: null },
      { period: '2nd', course: 'Free', location: '', type: 'Free', group: null },
      { period: '3rd', course: 'Free', location: '', type: 'Free', group: null },
      { period: '4th', course: 'Free', location: '', type: 'Free', group: null },
      { period: '5th', course: 'Free', location: '', type: 'Free', group: null }
    ]
  },
  {
    day: 'Sunday',
    slots: [
      { period: '1st', course: 'CSEN602', location: 'C5.309', type: 'Lecture', group: null },
      { period: '2nd', course: 'CSEN602', location: 'C5.309', type: 'Lecture', group: null },
      { period: '3rd', course: 'Free', location: '', type: 'Free', group: null },
      { period: '4th', course: 'MATH401', location: 'C5.205', type: 'Lecture', group: null },
      { period: '5th', course: 'MATH401', location: 'C5.205', type: 'Lecture', group: null }
    ]
  },
  {
    day: 'Monday',
    slots: [
      { period: '1st', course: 'CSEN701', location: 'C5.104', type: 'Lecture', group: null },
      { period: '2nd', course: 'CSEN701', location: 'C5.104', type: 'Lecture', group: null },
      { period: '3rd', course: 'CSEN602', location: 'Lab 1', type: 'Tutorial', group: '1' },
      { period: '4th', course: 'CSEN602', location: 'Lab 1', type: 'Tutorial', group: '1' },
      { period: '5th', course: 'Free', location: '', type: 'Free', group: null }
    ]
  },
  {
    day: 'Tuesday',
    slots: [
      { period: '1st', course: 'CSEN703', location: 'C5.309', type: 'Lecture', group: null },
      { period: '2nd', course: 'CSEN703', location: 'C5.309', type: 'Lecture', group: null },
      { period: '3rd', course: 'Free', location: '', type: 'Free', group: null },
      { period: '4th', course: 'CSEN701', location: 'Lab 2', type: 'Tutorial', group: '2' },
      { period: '5th', course: 'CSEN701', location: 'Lab 2', type: 'Tutorial', group: '2' }
    ]
  },
  {
    day: 'Wednesday',
    slots: [
      { period: '1st', course: 'MATH401', location: 'C5.205', type: 'Tutorial', group: '3' },
      { period: '2nd', course: 'MATH401', location: 'C5.205', type: 'Tutorial', group: '3' },
      { period: '3rd', course: 'CSEN602', location: 'C5.309', type: 'Lecture', group: null },
      { period: '4th', course: 'CSEN602', location: 'C5.309', type: 'Lecture', group: null },
      { period: '5th', course: 'Free', location: '', type: 'Free', group: null }
    ]
  },
  {
    day: 'Thursday',
    slots: [
      { period: '1st', course: 'CSEN703', location: 'Lab 3', type: 'Tutorial', group: '1' },
      { period: '2nd', course: 'CSEN703', location: 'Lab 3', type: 'Tutorial', group: '1' },
      { period: '3rd', course: 'CSEN701', location: 'C5.104', type: 'Lecture', group: null },
      { period: '4th', course: 'CSEN701', location: 'C5.104', type: 'Lecture', group: null },
      { period: '5th', course: 'Free', location: '', type: 'Free', group: null }
    ]
  },
  {
    day: 'Friday',
    slots: [
      { period: '1st', course: 'Free', location: '', type: 'Free', group: null },
      { period: '2nd', course: 'Free', location: '', type: 'Free', group: null },
      { period: '3rd', course: 'Free', location: '', type: 'Free', group: null },
      { period: '4th', course: 'Free', location: '', type: 'Free', group: null },
      { period: '5th', course: 'Free', location: '', type: 'Free', group: null }
    ]
  }
];

export const DEMO_ATTENDANCE_DATA = {
  'Course - CSEN602 Operating Systems': [
    {
      rowNumber: '1',
      attendance: 'Attended',
      sessionDescription: 'Lecture 1 - Introduction to OS @ 2024.02.05',
      slot: '1',
      date: '2024.02.05'
    },
    {
      rowNumber: '2',
      attendance: 'Attended',
      sessionDescription: 'Lecture 2 - Process Management @ 2024.02.07',
      slot: '2',
      date: '2024.02.07'
    },
    {
      rowNumber: '3',
      attendance: 'Absent',
      sessionDescription: 'Tutorial 1 - Process Scheduling @ 2024.02.12',
      slot: '3',
      date: '2024.02.12'
    },
    {
      rowNumber: '4',
      attendance: 'Attended',
      sessionDescription: 'Lecture 3 - Memory Management @ 2024.02.14',
      slot: '4',
      date: '2024.02.14'
    },
    {
      rowNumber: '5',
      attendance: 'Attended',
      sessionDescription: 'Tutorial 2 - Memory Allocation @ 2024.02.19',
      slot: '5',
      date: '2024.02.19'
    }
  ],
  'Course - CSEN701 Computer Networks': [
    {
      rowNumber: '1',
      attendance: 'Attended',
      sessionDescription: 'Lecture 1 - Network Fundamentals @ 2024.02.06',
      slot: '1',
      date: '2024.02.06'
    },
    {
      rowNumber: '2',
      attendance: 'Attended',
      sessionDescription: 'Lecture 2 - OSI Model @ 2024.02.08',
      slot: '2',
      date: '2024.02.08'
    },
    {
      rowNumber: '3',
      attendance: 'Attended',
      sessionDescription: 'Tutorial 1 - Network Protocols @ 2024.02.13',
      slot: '3',
      date: '2024.02.13'
    },
    {
      rowNumber: '4',
      attendance: 'Absent',
      sessionDescription: 'Lecture 3 - TCP/IP @ 2024.02.15',
      slot: '4',
      date: '2024.02.15'
    }
  ],
  'Course - MATH401 Probability & Statistics': [
    {
      rowNumber: '1',
      attendance: 'Attended',
      sessionDescription: 'Lecture 1 - Basic Probability @ 2024.02.04',
      slot: '1',
      date: '2024.02.04'
    },
    {
      rowNumber: '2',
      attendance: 'Attended',
      sessionDescription: 'Tutorial 1 - Probability Exercises @ 2024.02.11',
      slot: '2',
      date: '2024.02.11'
    },
    {
      rowNumber: '3',
      attendance: 'Attended',
      sessionDescription: 'Lecture 2 - Distributions @ 2024.02.18',
      slot: '3',
      date: '2024.02.18'
    }
  ]
};

export const DEMO_FINANCIALS_DATA = [
  {
    paymentDescription: 'Fall 2024 Tuition - 1st Installment',
    currency: 'EGP',
    amount: '25,000.00',
    dueDate: '2024-03-30'
  },
  {
    paymentDescription: 'Student Activities Fee',
    currency: 'EGP',
    amount: '500.00',
    dueDate: '2024-04-15'
  },
  {
    paymentDescription: 'Lab Equipment Fee',
    currency: 'EGP',
    amount: '1,200.00',
    dueDate: '2024-04-20'
  }
];

export const DEMO_TRANSCRIPT_DATA = [
  {
    year: '2023-2024',
    yearValue: '2023',
    semesters: [
      {
        tableIndex: 0,
        semesterTitle: 'Fall 2023',
        semesterGPA: '3.67',
        totalHours: '18',
        coursesCount: 6,
        courses: [
          {
            courseCode: 'CSEN601',
            courseName: 'Software Engineering',
            creditHours: '3',
            grade: 'A-',
            gradePoints: '11.1'
          },
          {
            courseCode: 'CSEN602',
            courseName: 'Operating Systems',
            creditHours: '3',
            grade: 'B+',
            gradePoints: '9.9'
          },
          {
            courseCode: 'CSEN701',
            courseName: 'Computer Networks',
            creditHours: '3',
            grade: 'A',
            gradePoints: '12.0'
          },
          {
            courseCode: 'MATH401',
            courseName: 'Probability & Statistics',
            creditHours: '3',
            grade: 'B',
            gradePoints: '9.0'
          },
          {
            courseCode: 'CSEN703',
            courseName: 'Advanced Computer Architecture',
            creditHours: '3',
            grade: 'A-',
            gradePoints: '11.1'
          },
          {
            courseCode: 'HUMN401',
            courseName: 'Technical Writing',
            creditHours: '3',
            grade: 'A',
            gradePoints: '12.0'
          }
        ]
      },
      {
        tableIndex: 1,
        semesterTitle: 'Spring 2024',
        semesterGPA: '3.83',
        totalHours: '15',
        coursesCount: 5,
        courses: [
          {
            courseCode: 'CSEN604',
            courseName: 'Database Systems',
            creditHours: '3',
            grade: 'A',
            gradePoints: '12.0'
          },
          {
            courseCode: 'CSEN605',
            courseName: 'Computer Graphics',
            creditHours: '3',
            grade: 'A-',
            gradePoints: '11.1'
          },
          {
            courseCode: 'CSEN702',
            courseName: 'Machine Learning',
            creditHours: '3',
            grade: 'A',
            gradePoints: '12.0'
          },
          {
            courseCode: 'ELCT601',
            courseName: 'Digital Signal Processing',
            creditHours: '3',
            grade: 'B+',
            gradePoints: '9.9'
          },
          {
            courseCode: 'MGMT401',
            courseName: 'Engineering Management',
            creditHours: '3',
            grade: 'A-',
            gradePoints: '11.1'
          }
        ]
      }
    ],
    summary: {
      totalCourses: 11,
      semestersWithCourses: 2
    }
  },
  {
    year: '2022-2023',
    yearValue: '2022',
    semesters: [
      {
        tableIndex: 0,
        semesterTitle: 'Fall 2022',
        semesterGPA: '3.42',
        totalHours: '18',
        coursesCount: 6,
        courses: [
          {
            courseCode: 'CSEN501',
            courseName: 'Algorithms & Data Structures',
            creditHours: '3',
            grade: 'B+',
            gradePoints: '9.9'
          },
          {
            courseCode: 'CSEN502',
            courseName: 'Object-Oriented Programming',
            creditHours: '3',
            grade: 'A',
            gradePoints: '12.0'
          },
          {
            courseCode: 'MATH301',
            courseName: 'Discrete Mathematics',
            creditHours: '3',
            grade: 'B',
            gradePoints: '9.0'
          },
          {
            courseCode: 'PHYS301',
            courseName: 'Electronics',
            creditHours: '3',
            grade: 'B+',
            gradePoints: '9.9'
          },
          {
            courseCode: 'CSEN503',
            courseName: 'Database Fundamentals',
            creditHours: '3',
            grade: 'A-',
            gradePoints: '11.1'
          },
          {
            courseCode: 'HUMN301',
            courseName: 'Professional Ethics',
            creditHours: '3',
            grade: 'A',
            gradePoints: '12.0'
          }
        ]
      },
      {
        tableIndex: 1,
        semesterTitle: 'Spring 2023',
        semesterGPA: '3.25',
        totalHours: '15',
        coursesCount: 5,
        courses: [
          {
            courseCode: 'CSEN504',
            courseName: 'Computer Organization',
            creditHours: '3',
            grade: 'B',
            gradePoints: '9.0'
          },
          {
            courseCode: 'CSEN505',
            courseName: 'Web Development',
            creditHours: '3',
            grade: 'A-',
            gradePoints: '11.1'
          },
          {
            courseCode: 'MATH302',
            courseName: 'Differential Equations',
            creditHours: '3',
            grade: 'B',
            gradePoints: '9.0'
          },
          {
            courseCode: 'ELCT301',
            courseName: 'Circuit Analysis',
            creditHours: '3',
            grade: 'B+',
            gradePoints: '9.9'
          },
          {
            courseCode: 'HUMN302',
            courseName: 'Advanced Writing',
            creditHours: '3',
            grade: 'A',
            gradePoints: '12.0'
          }
        ]
      }
    ],
    summary: {
      totalCourses: 11,
      semestersWithCourses: 2
    }
  },
  {
    year: '2021-2022',
    yearValue: '2021',
    semesters: [
      {
        tableIndex: 0,
        semesterTitle: 'Fall 2021',
        semesterGPA: '3.33',
        totalHours: '18',
        coursesCount: 6,
        courses: [
          {
            courseCode: 'CSEN401',
            courseName: 'Computer Programming',
            creditHours: '3',
            grade: 'B+',
            gradePoints: '9.9'
          },
          {
            courseCode: 'MATH201',
            courseName: 'Calculus I',
            creditHours: '3',
            grade: 'A-',
            gradePoints: '11.1'
          },
          {
            courseCode: 'PHYS201',
            courseName: 'Physics I',
            creditHours: '3',
            grade: 'B',
            gradePoints: '9.0'
          },
          {
            courseCode: 'CHEM201',
            courseName: 'Chemistry',
            creditHours: '3',
            grade: 'B+',
            gradePoints: '9.9'
          },
          {
            courseCode: 'HUMN201',
            courseName: 'English Composition',
            creditHours: '3',
            grade: 'A',
            gradePoints: '12.0'
          },
          {
            courseCode: 'MATH202',
            courseName: 'Linear Algebra',
            creditHours: '3',
            grade: 'B',
            gradePoints: '9.0'
          }
        ]
      },
      {
        tableIndex: 1,
        semesterTitle: 'Spring 2022',
        semesterGPA: '3.50',
        totalHours: '15',
        coursesCount: 5,
        courses: [
          {
            courseCode: 'CSEN402',
            courseName: 'Data Structures',
            creditHours: '3',
            grade: 'A-',
            gradePoints: '11.1'
          },
          {
            courseCode: 'MATH301',
            courseName: 'Calculus II',
            creditHours: '3',
            grade: 'B+',
            gradePoints: '9.9'
          },
          {
            courseCode: 'PHYS202',
            courseName: 'Physics II',
            creditHours: '3',
            grade: 'B+',
            gradePoints: '9.9'
          },
          {
            courseCode: 'ELCT201',
            courseName: 'Digital Logic',
            creditHours: '3',
            grade: 'A',
            gradePoints: '12.0'
          },
          {
            courseCode: 'HUMN202',
            courseName: 'Technical Communication',
            creditHours: '3',
            grade: 'A-',
            gradePoints: '11.1'
          }
        ]
      }
    ],
    summary: {
      totalCourses: 11,
      semestersWithCourses: 2
    }
  }
];

export const DEMO_CUMULATIVE_GPA = '1.59';

export const DEMO_EVALUATE_COURSES = [
  {
    code: 'CSEN602', 
    name: 'Operating Systems - CSEN602',
    value: 'csen602_os',
    text: 'Operating Systems - CSEN602'
  },
  {
    code: 'CSEN701',
    name: 'Computer Networks - CSEN701', 
    value: 'csen701_networks',
    text: 'Computer Networks - CSEN701'
  },
  {
    code: 'MATH401',
    name: 'Probability & Statistics - MATH401',
    value: 'math401_stats', 
    text: 'Probability & Statistics - MATH401'
  },
  {
    code: 'CSEN703',
    name: 'Advanced Computer Architecture - CSEN703',
    value: 'csen703_arch',
    text: 'Advanced Computer Architecture - CSEN703'
  }
];

export const DEMO_EVALUATE_STAFF = [
  {
    code: 'sarah_ahmed',
    name: 'Dr. Sarah Ahmed',
    value: 'staff_sarah_ahmed',
    text: 'Dr. Sarah Ahmed',
    isEvaluated: false // Can be evaluated
  },
  {
    code: 'mohamed_ali',
    name: 'Prof. Mohamed Ali',
    value: 'staff_mohamed_ali', 
    text: 'Prof. Mohamed Ali',
    isEvaluated: true // Already evaluated
  },
  {
    code: 'omar_hassan',
    name: 'Eng. Omar Hassan',
    value: 'staff_omar_hassan',
    text: 'Eng. Omar Hassan',
    isEvaluated: false // Can be evaluated
  },
  {
    code: 'fatma_hassan',
    name: 'Dr. Fatma Hassan',
    value: 'staff_fatma_hassan',
    text: 'Dr. Fatma Hassan',
    isEvaluated: true // Already evaluated
  },
  {
    code: 'nadia_mahmoud',
    name: 'Dr. Nadia Mahmoud',
    value: 'staff_nadia_mahmoud',
    text: 'Dr. Nadia Mahmoud',
    isEvaluated: false // Can be evaluated
  }
];

export const DEMO_ACADEMIC_YEARS = [
  { value: '2023', text: '2023-2024' },
  { value: '2022', text: '2022-2023' },
  { value: '2021', text: '2021-2022' }
];

// Helper function to check if credentials are demo credentials
export const isDemoUser = (username, password) => {
  console.log('🔍 isDemoUser check: provided username="' + username + '", password="' + password + '"');
  console.log('🔍 isDemoUser check: expected username="' + DEMO_CREDENTIALS.username + '", password="' + DEMO_CREDENTIALS.password + '"');
  const result = username === DEMO_CREDENTIALS.username && password === DEMO_CREDENTIALS.password;
  console.log('🔍 isDemoUser result:', result);
  return result;
};

// Helper function to get demo data for attendance dropdown
export const getDemoAttendanceDropdown = () => {
  return Object.keys(DEMO_ATTENDANCE_DATA).map(courseName => ({
    value: courseName,
    text: courseName
  }));
};

// Helper function to get demo data for grades dropdown
export const getDemoGradesDropdown = () => {
  return Object.keys(DEMO_GRADES_DATA).map(courseName => ({
    value: courseName,
    text: courseName
  }));
};

// Helper function to get demo midterm grade for a course
export const getDemoMidtermGrade = (courseName) => {
  return DEMO_MIDTERM_GRADES[courseName] || null;
};

// Demo CMS data for different semesters
export const DEMO_CMS_DATA = {
  'Fall 2024': [
    {
      id: 'cms_001',
      sid: 'sem_fall_2024',
      courseCode: 'CSEN602',
      courseName: 'Operating Systems',
      instructor: 'Dr. Sarah Ahmed',
      credits: 3
    },
    {
      id: 'cms_002',
      sid: 'sem_fall_2024',
      courseCode: 'CSEN701',
      courseName: 'Computer Networks',
      instructor: 'Prof. Mohamed Ali',
      credits: 3
    },
    {
      id: 'cms_003',
      sid: 'sem_fall_2024',
      courseCode: 'MATH401',
      courseName: 'Probability & Statistics',
      instructor: 'Dr. Fatma Hassan',
      credits: 3
    },
    {
      id: 'cms_004',
      sid: 'sem_fall_2024',
      courseCode: 'CSEN703',
      courseName: 'Advanced Computer Architecture',
      instructor: 'Dr. Omar Khaled',
      credits: 3
    },
    {
      id: 'cms_005',
      sid: 'sem_fall_2024',
      courseCode: 'ELCT601',
      courseName: 'Digital Signal Processing',
      instructor: 'Dr. Nadia Mahmoud',
      credits: 3
    }
  ],
  'Spring 2024': [
    {
      id: 'cms_006',
      sid: 'sem_spring_2024',
      courseCode: 'CSEN604',
      courseName: 'Software Engineering',
      instructor: 'Dr. Ahmed Mostafa',
      credits: 3
    },
    {
      id: 'cms_007',
      sid: 'sem_spring_2024',
      courseCode: 'CSEN702',
      courseName: 'Database Systems',
      instructor: 'Prof. Laila Ibrahim',
      credits: 3
    },
    {
      id: 'cms_008',
      sid: 'sem_spring_2024',
      courseCode: 'MATH402',
      courseName: 'Numerical Analysis',
      instructor: 'Dr. Hassan Ali',
      credits: 3
    },
    {
      id: 'cms_009',
      sid: 'sem_spring_2024',
      courseCode: 'ELCT602',
      courseName: 'Control Systems',
      instructor: 'Dr. Mona Farid',
      credits: 3
    },
    {
      id: 'cms_010',
      sid: 'sem_spring_2024',
      courseCode: 'CSEN705',
      courseName: 'Machine Learning',
      instructor: 'Dr. Youssef Ahmed',
      credits: 3
    }
  ]
};

// Demo course content data (for ContentScreen)
export const DEMO_COURSE_CONTENT = {
  'CSEN604': {
    courseCode: 'CSEN604',
    courseName: 'Software Engineering',
    courseDescription: 'This course covers the fundamental principles and practices of software engineering, including software development life cycles, requirements analysis, design patterns, testing methodologies, and project management.',
    announcements: [
      {
        id: 'ann-1',
        title: 'Final Exam Information',
        date: '9/4/2025',
        content: 'The Final will be from Lecture 6 to Lecture 11.\nAny content that was already examined in the quiz will not be included again in the final exam.',
        isImportant: true
      },
      {
        id: 'ann-2',
        title: 'Project Teams Formation',
        date: '8/28/2025',
        content: 'Project teams should be formed by September 10th. Each team should consist of 4-5 members. Please submit your team formation through the portal.',
        isImportant: false
      }
    ],
    filterTypes: ['Assignment', 'Assignment Solution', 'Exam Solution', 'Lecture Slides', 'Other', 'Project'],
    weeks: [
      {
        weekId: 'week-2025-5-26',
        weekTitle: 'Week: 2025-5-26',
        weekNumber: 12,
        startDate: '2025-05-26',
        endDate: '2025-06-01',
        materials: [
          {
            id: 'starter-code',
            type: 'Project',
            title: 'Starter Code',
            description: 'Starter code for final project implementation',
            url: 'demo://starter_code.zip',
            downloadable: true,
            size: '2.5 MB',
            uploadDate: '2025-05-26'
          },
          {
            id: 'compensation-task',
            type: 'Assignment',
            title: 'Compensation Task',
            description: 'Compensation assignment for missed lab sessions',
            url: 'demo://compensation_task.pdf',
            downloadable: true,
            size: '1.2 MB',
            uploadDate: '2025-05-27'
          },
          {
            id: 'lecture-recording-12',
            type: 'Video',
            title: 'Lecture 12 Recording - Software Testing',
            description: 'Video recording of lecture on software testing methodologies',
            url: 'demo://lecture_12_recording.mp4',
            downloadable: true,
            size: '145 MB',
            uploadDate: '2025-05-26'
          }
        ]
      },
      {
        weekId: 'week-2025-5-10',
        weekTitle: 'Week: 2025-5-10',
        weekNumber: 10,
        startDate: '2025-05-10',
        endDate: '2025-05-16',
        materials: [
          {
            id: 'practice-assignment-10',
            type: 'Assignment',
            title: 'Practice Assignment 10',
            description: 'Practice problems for software testing methodologies',
            url: 'demo://practice_assignment_10.pdf',
            downloadable: true,
            size: '856 KB',
            uploadDate: '2025-05-10'
          },
          {
            id: 'practice-assignment-10-solution',
            type: 'Assignment Solution',
            title: 'Practice Assignment 10 Solution',
            description: 'Solution for Practice Assignment 10',
            url: 'demo://practice_assignment_10_solution.pdf',
            downloadable: true,
            size: '1.1 MB',
            uploadDate: '2025-05-12'
          },
          {
            id: 'practice-assignment-9',
            type: 'Assignment',
            title: 'Practice Assignment 9',
            description: 'Practice problems for design patterns',
            url: 'demo://practice_assignment_9.pdf',
            downloadable: true,
            size: '742 KB',
            uploadDate: '2025-05-11'
          }
        ]
      },
      {
        weekId: 'week-2025-5-3',
        weekTitle: 'Week: 2025-5-3',
        weekNumber: 9,
        startDate: '2025-05-03',
        endDate: '2025-05-09',
        materials: [
          {
            id: 'lecture-slides-9',
            type: 'Lecture Slides',
            title: 'Lecture 9 - Software Testing',
            description: 'Unit testing, integration testing, and test-driven development',
            url: 'demo://lecture_9_slides.pdf',
            downloadable: true,
            size: '3.2 MB',
            uploadDate: '2025-05-03'
          },
          {
            id: 'midterm-solution',
            type: 'Exam Solution',
            title: 'Midterm Exam Solution',
            description: 'Complete solutions for midterm examination',
            url: 'demo://midterm_solution.pdf',
            downloadable: true,
            size: '2.8 MB',
            uploadDate: '2025-05-05'
          }
        ]
      },
      {
        weekId: 'week-2025-4-26',
        weekTitle: 'Week: 2025-4-26',
        weekNumber: 8,
        startDate: '2025-04-26',
        endDate: '2025-05-02',
        materials: [
          {
            id: 'project-guidelines',
            type: 'Project',
            title: 'Final Project Guidelines',
            description: 'Complete guidelines and requirements for the final project',
            url: 'demo://project_guidelines.pdf',
            downloadable: true,
            size: '1.5 MB',
            uploadDate: '2025-04-26'
          },
          {
            id: 'lecture-slides-8',
            type: 'Lecture Slides',
            title: 'Lecture 8 - Design Patterns',
            description: 'Common software design patterns and their applications',
            url: 'demo://lecture_8_slides.pdf',
            downloadable: true,
            size: '4.1 MB',
            uploadDate: '2025-04-27'
          }
        ]
      }
    ]
  },
  'CSEN602': {
    courseCode: 'CSEN602',
    courseName: 'Operating Systems',
    courseDescription: 'This course provides an in-depth study of operating system concepts including process management, memory management, file systems, and system security.',
    announcements: [
      {
        id: 'ann-os-1',
        title: 'Assignment 4 Due Soon',
        date: '9/4/2025',
        content: 'Assignment 4 on process scheduling is due tomorrow at 11:59 PM. Please submit through the course portal. Late submissions will incur penalty.',
        isImportant: true
      },
      {
        id: 'ann-os-2',
        title: 'Midterm Preparation',
        date: '8/30/2025',
        content: 'Midterm exam will cover chapters 1-5. Review sessions will be held this week in the lab.',
        isImportant: false
      }
    ],
    filterTypes: ['Assignment', 'Assignment Solution', 'Exam Solution', 'Lecture Slides', 'Other', 'Project'],
    weeks: [
      {
        weekId: 'week-2025-9-2',
        weekTitle: 'Week: 2025-9-2',
        weekNumber: 4,
        startDate: '2025-09-02',
        endDate: '2025-09-08',
        materials: [
          {
            id: 'assignment-4',
            type: 'Assignment',
            title: 'Assignment 4 - Process Scheduling',
            description: 'Implementation of various CPU scheduling algorithms',
            url: 'demo://os_assignment_4.pdf',
            downloadable: true,
            size: '1.8 MB',
            uploadDate: '2025-09-02'
          },
          {
            id: 'lecture-slides-4',
            type: 'Lecture Slides',
            title: 'Lecture 4 - CPU Scheduling',
            description: 'Scheduling algorithms: FCFS, SJF, Round Robin, Priority',
            url: 'demo://os_lecture_4_slides.pdf',
            downloadable: true,
            size: '2.9 MB',
            uploadDate: '2025-09-03'
          },
          {
            id: 'lab-demo-video',
            type: 'Video',
            title: 'Lab Demo - Process Synchronization',
            description: 'Video demonstration of process synchronization concepts',
            url: 'demo://os_lab_demo.mp4',
            downloadable: true,
            size: '78 MB',
            uploadDate: '2025-09-04'
          }
        ]
      },
      {
        weekId: 'week-2025-8-26',
        weekTitle: 'Week: 2025-8-26',
        weekNumber: 3,
        startDate: '2025-08-26',
        endDate: '2025-09-01',
        materials: [
          {
            id: 'assignment-3-solution',
            type: 'Assignment Solution',
            title: 'Assignment 3 Solution - Process Synchronization',
            description: 'Complete solution for process synchronization problems',
            url: 'demo://os_assignment_3_solution.pdf',
            downloadable: true,
            size: '1.4 MB',
            uploadDate: '2025-08-28'
          },
          {
            id: 'lab-code-examples',
            type: 'Project',
            title: 'Lab Code Examples Package',
            description: 'C code examples for process creation and IPC',
            url: 'demo://os_lab_examples.zip',
            downloadable: true,
            size: '512 KB',
            uploadDate: '2025-08-29'
          }
        ]
      }
    ]
  },
  'CSEN701': {
    courseCode: 'CSEN701',
    courseName: 'Computer Networks',
    courseDescription: 'This course covers computer network fundamentals, protocols, network architecture, and performance analysis.',
    announcements: [
      {
        id: 'ann-net-1',
        title: 'Lab Quiz Next Week',
        date: '9/4/2025',
        content: 'There will be a lab quiz on TCP/IP protocols next Tuesday. Please review your lab materials and Wireshark exercises.',
        isImportant: true
      }
    ],
    filterTypes: ['Assignment', 'Assignment Solution', 'Exam Solution', 'Lecture Slides', 'Other', 'Project'],
    weeks: [
      {
        weekId: 'week-2025-9-2',
        weekTitle: 'Week: 2025-9-2',
        weekNumber: 4,
        startDate: '2025-09-02',
        endDate: '2025-09-08',
        materials: [
          {
            id: 'wireshark-tutorial',
            type: 'Other',
            title: 'Wireshark Tutorial',
            description: 'Complete guide to packet analysis with Wireshark',
            url: 'demo://wireshark_tutorial.pdf',
            downloadable: true,
            size: '3.5 MB',
            uploadDate: '2025-09-02'
          },
          {
            id: 'tcp-lab-assignment',
            type: 'Assignment',
            title: 'TCP Protocol Analysis',
            description: 'Lab assignment on TCP connection establishment and termination',
            url: 'demo://tcp_lab_assignment.pdf',
            downloadable: true,
            size: '1.1 MB',
            uploadDate: '2025-09-04'
          }
        ]
      }
    ]
  },
  'CSEN703': {
    courseCode: 'CSEN703',
    courseName: 'Computer Architecture',
    courseDescription: 'Advanced topics in computer architecture including parallel processing, memory hierarchies, and performance optimization.',
    announcements: [
      {
        id: 'ann-arch-1',
        title: 'Guest Lecture - Industry Expert',
        date: '9/3/2025',
        content: 'Industry expert will give a guest lecture on modern CPU architecture and performance optimization techniques.',
        isImportant: false
      }
    ],
    filterTypes: ['Assignment', 'Assignment Solution', 'Exam Solution', 'Lecture Slides', 'Other', 'Project'],
    weeks: [
      {
        weekId: 'week-2025-9-1',
        weekTitle: 'Week: 2025-9-1',
        weekNumber: 3,
        startDate: '2025-09-01',
        endDate: '2025-09-07',
        materials: [
          {
            id: 'pipeline-simulation',
            type: 'Project',
            title: 'Pipeline Simulation Project',
            description: 'Implement a 5-stage MIPS pipeline simulator',
            url: 'demo://pipeline_project.pdf',
            downloadable: true,
            size: '2.2 MB',
            uploadDate: '2025-09-01'
          }
        ]
      }
    ]
  },
  'MATH401': {
    courseCode: 'MATH401',
    courseName: 'Probability and Statistics',
    courseDescription: 'Introduction to probability theory, statistical distributions, and statistical inference methods.',
    announcements: [
      {
        id: 'ann-math-1',
        title: 'Makeup Exam',
        date: '9/1/2025',
        content: 'Makeup exam for Quiz 2 will be held on September 7th at 10:00 AM in Room C5.205.',
        isImportant: true
      }
    ],
    filterTypes: ['Assignment', 'Assignment Solution', 'Exam Solution', 'Lecture Slides', 'Other', 'Project'],
    weeks: [
      {
        weekId: 'week-2025-8-30',
        weekTitle: 'Week: 2025-8-30',
        weekNumber: 3,
        startDate: '2025-08-30',
        endDate: '2025-09-05',
        materials: [
          {
            id: 'extra-credit',
            type: 'Assignment',
            title: 'Extra Credit Assignment',
            description: 'Optional assignment worth 5% of final grade',
            url: 'demo://extra_credit_assignment.pdf',
            downloadable: true,
            size: '890 KB',
            uploadDate: '2025-08-30'
          }
        ]
      }
    ]
  },
  'ELCT601': {
    courseCode: 'ELCT601',
    courseName: 'Digital Signal Processing',
    courseDescription: 'Digital signal processing fundamentals including discrete-time signals, transforms, and filter design.',
    announcements: [
      {
        id: 'ann-dsp-1',
        title: 'Filter Design Assignment',
        date: '9/2/2025',
        content: 'Assignment 3: Digital Filter Design has been released. Due date: September 16th, 2025.',
        isImportant: true
      }
    ],
    filterTypes: ['Assignment', 'Assignment Solution', 'Exam Solution', 'Lecture Slides', 'Other', 'Project'],
    weeks: [
      {
        weekId: 'week-2025-9-2',
        weekTitle: 'Week: 2025-9-2',
        weekNumber: 4,
        startDate: '2025-09-02',
        endDate: '2025-09-08',
        materials: [
          {
            id: 'filter-design-assignment',
            type: 'Assignment',
            title: 'Digital Filter Design',
            description: 'Design FIR and IIR filters for given specifications',
            url: 'demo://filter_design_assignment.pdf',
            downloadable: true,
            size: '1.7 MB',
            uploadDate: '2025-09-02'
          },
          {
            id: 'matlab-examples',
            type: 'Other',
            title: 'MATLAB Filter Examples',
            description: 'MATLAB code examples for digital filter implementation',
            url: 'demo://matlab_filter_examples.zip',
            downloadable: true,
            size: '2.8 MB',
            uploadDate: '2025-09-03'
          }
        ]
      }
    ]
  }
};

// Helper function to get demo CMS dropdown options
export const getDemoCMSDropdown = () => {
  return Object.keys(DEMO_CMS_DATA).map(semester => ({
    value: semester,
    text: semester
  }));
};

// Helper function to get demo course content
export const getDemoCourseContent = (courseCode) => {
  return DEMO_COURSE_CONTENT[courseCode] || null;
};

// Helper function to check if user is in demo mode
export const checkDemoMode = async () => {
  try {
    const demoFlag = await AsyncStorage.getItem('is_demo_user');
    console.log('🔍 checkDemoMode: is_demo_user =', demoFlag, 'returning:', demoFlag === 'true');
    return demoFlag === 'true';
  } catch (error) {
    console.log('Error in checkDemoMode:', error);
    return false;
  }
};
