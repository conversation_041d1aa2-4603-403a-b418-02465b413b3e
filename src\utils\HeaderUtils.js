import { Dimensions } from 'react-native';

/**
 * HeaderUtils - Utility functions for dynamic header layout management
 * Provides functionality to center titles and handle overflow scenarios
 */

/**
 * Calculate dynamic header layout properties
 * @param {string} title - The title text
 * @param {object} options - Configuration options
 * @returns {object} Layout properties for header elements
 */
export const calculateHeaderLayout = (title = '', options = {}) => {
  const {
    minButtonSpacing = 20, // Minimum space from screen edges
    buttonWidth = 50, // Width of sidebar/back and refresh buttons
    minTitleMargin = 10, // Minimum margin between title and buttons
    baseFontSize = 30, // Base font size for title
    maxFontSize = 30, // Maximum font size
    minFontSize = 18, // Minimum font size
    titlePadding = 20, // Horizontal padding for title container
  } = options;

  // Get screen dimensions
  const { width: screenWidth } = Dimensions.get('window');
  
  // Calculate available space for title
  const buttonSpaceLeft = minButtonSpacing + buttonWidth + minTitleMargin;
  const buttonSpaceRight = minButtonSpacing + buttonWidth + minTitleMargin;
  const availableWidth = screenWidth - buttonSpaceLeft - buttonSpaceRight - titlePadding;
  
  // Estimate title width (rough approximation: 0.6 * fontSize * character count)
  const estimatedCharWidth = baseFontSize * 0.6;
  const estimatedTitleWidth = title.length * estimatedCharWidth;
  
  // Calculate if title will overflow at base font size
  const willOverflow = estimatedTitleWidth > availableWidth;
  
  // Calculate dynamic font size if overflow occurs
  let dynamicFontSize = baseFontSize;
  let dynamicHeight = 100; // Base header height
  
  if (willOverflow && title.length > 0) {
    // Calculate font size that fits available width
    const targetCharWidth = availableWidth / title.length;
    const calculatedFontSize = Math.max(targetCharWidth / 0.6, minFontSize);
    dynamicFontSize = Math.min(calculatedFontSize, maxFontSize);
    
    // If even minimum font size doesn't fit, increase header height
    const minTitleWidth = title.length * (minFontSize * 0.6);
    if (minTitleWidth > availableWidth) {
      // Calculate additional height needed for text wrapping
      const lines = Math.ceil(minTitleWidth / availableWidth);
      dynamicHeight = Math.max(100, 80 + (lines * 25)); // Base height + line height * additional lines
      dynamicFontSize = minFontSize;
    }
  }
  
  return {
    // Title container properties
    titleContainer: {
      position: 'absolute',
      top: 25, // Moved higher up from 55
      left: buttonSpaceLeft,
      right: buttonSpaceRight,
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 999,
      minHeight: 40,
    },
    
    // Title text properties
    titleText: {
      fontSize: Math.round(dynamicFontSize),
      fontWeight: 'bold',
      textAlign: 'center',
      numberOfLines: willOverflow ? 2 : 1,
      allowFontScaling: false, // Prevent system font scaling from breaking layout
    },
    
    // Button container properties (consistent positioning)
    leftButtonContainer: {
      position: 'absolute',
      top: 20, // Moved higher up from 50
      left: minButtonSpacing,
      zIndex: 1000,
    },
    
    rightButtonContainer: {
      position: 'absolute',
      top: 20, // Moved higher up from 50
      right: minButtonSpacing,
      zIndex: 1000,
    },
    
    // Layout metadata
    layout: {
      headerHeight: Math.max(80, dynamicHeight), // Reduced from 100 to 80 for tighter spacing
      titleFontSize: Math.round(dynamicFontSize),
      willOverflow,
      availableWidth,
      estimatedTitleWidth,
      isWrapping: willOverflow && dynamicFontSize === minFontSize,
    }
  };
};

/**
 * Get responsive header layout for different screen sizes
 * @param {string} title - The title text
 * @param {object} theme - Theme object
 * @param {string} currentThemeName - Current theme name
 * @returns {object} Complete header layout with styles
 */
export const getResponsiveHeaderLayout = (title, theme, currentThemeName = 'dark') => {
  const { width: screenWidth, height: screenHeight } = Dimensions.get('window');
  
  // Determine screen size category
  const isVerySmallScreen = screenWidth < 350 || screenHeight < 600;
  const isSmallScreen = screenWidth < 375 || screenHeight < 700;
  
  // Adjust options based on screen size
  const options = {
    minButtonSpacing: isVerySmallScreen ? 15 : 20,
    buttonWidth: 50,
    minTitleMargin: isVerySmallScreen ? 8 : isSmallScreen ? 10 : 12,
    baseFontSize: isVerySmallScreen ? 24 : isSmallScreen ? 28 : 30,
    maxFontSize: isVerySmallScreen ? 24 : isSmallScreen ? 28 : 30,
    minFontSize: isVerySmallScreen ? 16 : 18,
    titlePadding: isVerySmallScreen ? 10 : 20,
  };
  
  const layout = calculateHeaderLayout(title, options);
  
  // Add theme-specific styling
  const safeCurrentThemeName = currentThemeName || 'dark';
  
  return {
    ...layout,
    
    // Enhanced styles with theme integration
    styles: {
      titleContainer: {
        ...layout.titleContainer,
      },
      
      titleText: {
        ...layout.titleText,
        color: safeCurrentThemeName === 'colorful' ? '#FFFFFF' : theme.colors.text,
      },
      
      buttonContainer: {
        backgroundColor: theme.colors.surface,
        borderRadius: 25,
        width: 50,
        height: 50,
        justifyContent: 'center',
        alignItems: 'center',
        shadowColor: theme.colors.shadow,
        shadowOffset: {
          width: 0,
          height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
        borderWidth: safeCurrentThemeName === 'colorful' ? 3 : safeCurrentThemeName === 'navy' ? 2 : 0,
        borderColor: safeCurrentThemeName === 'colorful' ? '#FFD700' : safeCurrentThemeName === 'navy' ? '#DC2626' : 'transparent',
      },
      
      buttonContainerLoading: {
        backgroundColor: theme.colors.surfaceSecondary,
        opacity: 0.7,
      },
    }
  };
};

/**
 * Calculate main content padding based on dynamic header height
 * @param {object} headerLayout - Header layout object from calculateHeaderLayout
 * @param {number} additionalPadding - Additional padding to add
 * @returns {object} Content container styling
 */
export const getContentPaddingForHeader = (headerLayout, additionalPadding = 0) => {
  const paddingTop = headerLayout.layout.headerHeight + additionalPadding;
  
  return {
    contentContainer: {
      flex: 1,
      paddingTop,
    }
  };
};

/**
 * Validate title length and suggest truncation if needed
 * @param {string} title - The title text
 * @param {number} maxLength - Maximum recommended length
 * @returns {object} Validation result
 */
export const validateTitleLength = (title, maxLength = 25) => {
  const isValid = title.length <= maxLength;
  const truncated = isValid ? title : `${title.substring(0, maxLength - 3)}...`;
  
  return {
    isValid,
    original: title,
    truncated,
    length: title.length,
    maxLength,
    suggestion: isValid ? null : `Consider shortening title from ${title.length} to ${maxLength} characters`,
  };
};

export default {
  calculateHeaderLayout,
  getResponsiveHeaderLayout,
  getContentPaddingForHeader,
  validateTitleLength,
};