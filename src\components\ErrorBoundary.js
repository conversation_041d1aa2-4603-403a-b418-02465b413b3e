import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import DebugUtils from '../utils/DebugUtils';

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to our debug system
    DebugUtils.logComponentError(error, errorInfo);
    
    this.setState({
      error,
      errorInfo
    });

    // Log additional context
    console.error('🚨 ErrorBoundary caught an error:', error);
    console.error('🚨 Error Info:', errorInfo);
  }

  handleRestart = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      showDetails: false
    });
  };

  toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails
    }));
  };

  exportLogs = async () => {
    try {
      const logs = await DebugUtils.exportLogs();
      console.log('📋 Exported logs for debugging:', logs);
      // In a real app, you might want to share these logs or send them to a server
    } catch (error) {
      console.error('❌ Failed to export logs:', error);
    }
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo } = this.state;
      
      return (
        <View style={styles.container}>
          <View style={styles.header}>
            <Text style={styles.title}>🚨 App Error</Text>
            <Text style={styles.subtitle}>
              Something went wrong. The error has been logged for debugging.
            </Text>
          </View>

          <View style={styles.actions}>
            <TouchableOpacity style={styles.button} onPress={this.handleRestart}>
              <Text style={styles.buttonText}>🔄 Restart App</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.button} onPress={this.exportLogs}>
              <Text style={styles.buttonText}>📋 Export Debug Logs</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.debugButton} onPress={this.toggleDetails}>
              <Text style={styles.debugButtonText}>
                {this.state.showDetails ? '🔼 Hide Details' : '🔽 Show Debug Details'}
              </Text>
            </TouchableOpacity>
          </View>

          {this.state.showDetails && (
            <ScrollView style={styles.debugContainer}>
              <Text style={styles.debugTitle}>Error Details:</Text>
              <Text style={styles.debugText}>
                {error?.toString() || 'Unknown error'}
              </Text>
              
              {error?.stack && (
                <>
                  <Text style={styles.debugTitle}>Stack Trace:</Text>
                  <Text style={styles.debugText}>{error.stack}</Text>
                </>
              )}
              
              {errorInfo?.componentStack && (
                <>
                  <Text style={styles.debugTitle}>Component Stack:</Text>
                  <Text style={styles.debugText}>{errorInfo.componentStack}</Text>
                </>
              )}
              
              <Text style={styles.debugTitle}>System Info:</Text>
              <Text style={styles.debugText}>
                Production: {DebugUtils.isProduction ? 'Yes' : 'No'}{'\n'}
                Hermes: {DebugUtils.isHermesEnabled ? 'Yes' : 'No'}{'\n'}
                Timestamp: {new Date().toISOString()}
              </Text>
            </ScrollView>
          )}
        </View>
      );
    }

    return this.props.children;
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#3A3A3A', // Updated to match new dark theme surface color
    padding: 20,
    justifyContent: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#ff4444',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#cccccc',
    textAlign: 'center',
    lineHeight: 22,
  },
  actions: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  debugButton: {
    backgroundColor: '#2196F3',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  debugButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: 'bold',
  },
  debugContainer: {
    flex: 1,
    backgroundColor: '#4A4A4A', // Updated to match new dark theme surfaceSecondary color
    padding: 15,
    borderRadius: 8,
    maxHeight: 300,
  },
  debugTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#ffaa00',
    marginTop: 10,
    marginBottom: 5,
  },
  debugText: {
    fontSize: 12,
    color: '#cccccc',
    fontFamily: 'monospace',
    lineHeight: 16,
  },
});

export default ErrorBoundary;
