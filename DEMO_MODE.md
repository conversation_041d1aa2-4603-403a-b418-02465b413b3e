# Demo Mode Feature

This document describes the demo mode feature that allows users to experience the app with realistic dummy data without needing actual GUC credentials.

## How to Access Demo Mode

To enter demo mode, use the following credentials on the login screen:

- **Username**: `demo`
- **Password**: `demo123`

## What Demo Mode Includes

When logged in as a demo user, the app will:

1. **Skip WebView Authentication**: No actual connection to the GUC website is made
2. **Display Realistic Data**: All screens (except Mail and CMS) show realistic dummy data
3. **Maintain Full Functionality**: All UI interactions work normally, including:
   - Navigation between screens
   - Dropdown selections
   - Refresh buttons (with simulated loading)
   - Theme switching
   - Sidebar functionality

## Screens with Demo Data

### Portal Screen
- Demo student information (<PERSON>)
- Demo notifications with realistic content

### Grades Screen
- 4 demo courses with realistic grades
- Course selection dropdown works
- Grade animations and calculations function normally

### Exams Screen
- 4 upcoming exams with realistic details
- Proper date formatting and status indicators

### Schedule Screen
- Full weekly schedule with realistic course slots
- Day selection and navigation works

### Attendance Screen
- Attendance records for demo courses
- Attendance statistics and warnings

### Financials Screen
- Sample payment records with due dates
- Realistic amounts in EGP currency

### Transcript Screen
- Multi-year academic transcript
- Semester breakdown with courses and GPAs
- Cumulative GPA calculation

### Evaluate Screen
- List of courses available for evaluation
- (Note: Actual evaluation submission is not implemented in demo mode)

## Screens NOT Included in Demo Mode

- **Mail Screen**: Requires actual O365 authentication
- **CMS Screen**: Requires actual course management system access

These screens will still be accessible but will not have demo data.

## Technical Implementation

### Files Added/Modified

1. **`src/utils/DemoData.js`**: Contains all demo data constants
2. **`src/utils/DemoModeHelper.js`**: Helper functions for demo mode operations
3. **`src/screens/LoginScreen.js`**: Modified to detect demo credentials
4. **All Portal screens**: Modified to use demo data when in demo mode

### Demo Mode Detection

The app detects demo mode by:
1. Checking credentials during login
2. Setting a flag in AsyncStorage (`is_demo_user: 'true'`)
3. Each screen checks this flag and uses demo data accordingly

### Data Persistence

Demo mode data is:
- Stored in memory during the session
- Not cached to AsyncStorage (to avoid conflicts with real data)
- Reset when logging out

## Benefits

1. **Easy Demonstration**: Perfect for showcasing app features
2. **No Dependencies**: Works without internet or GUC credentials
3. **Realistic Experience**: Users see how the app would look with real data
4. **Safe Testing**: No risk of affecting real user data

## Usage Scenarios

- **App demonstrations** to stakeholders
- **User testing** without requiring GUC access
- **Development testing** of UI components
- **Screenshots** for documentation or marketing

## Limitations

- No actual data synchronization
- Mail and CMS screens remain non-functional
- Evaluation submissions are not processed
- Refresh operations only simulate loading delays

## Future Enhancements

Potential improvements for demo mode:
- Add more diverse demo data
- Include demo data for Mail screen
- Add demo CMS content
- Implement demo evaluation submission flow
