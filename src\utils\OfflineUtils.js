import AsyncStorage from '@react-native-async-storage/async-storage';
import { loadFromCache } from './CacheManager';

/**
 * Utility functions for offline mode functionality
 */

/**
 * Check if cached data exists for a specific feature
 * @param {string} featureName - Name of the feature to check
 * @returns {Promise<boolean>} - Whether cached data exists
 */
export const hasCachedData = async (featureName) => {
  try {
    switch (featureName.toLowerCase()) {
      case 'grades':
        return await hasGradesCachedData();
      case 'attendance':
        return await hasAttendanceCachedData();
      case 'schedule':
        return await hasScheduleCachedData();
      case 'exams':
        return await hasExamsCachedData();
      case 'transcript':
        return await hasTranscriptCachedData();
      case 'financials':
        return await hasFinancialsCachedData();
      case 'notifications':
        return await hasNotificationsCachedData();
      case 'student_info':
        return await hasStudentInfoCachedData();
      default:
        return false;
    }
  } catch (error) {
    console.log(`❌ Error checking cached data for ${featureName}:`, error);
    return false;
  }
};

/**
 * Get list of cached courses for grades
 * @returns {Promise<Array>} - Array of cached course codes
 */
export const getCachedGradesCourses = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const gradeKeys = keys.filter(key => 
      key.includes('grades_cache_') && 
      !key.includes('timestamp') &&
      !key.includes('demo_') // Exclude demo data
    );
    
    const courses = gradeKeys.map(key => {
      const parts = key.split('grades_cache_');
      return parts[1]; // Extract course code
    }).filter(Boolean);
    
    console.log('📊 Cached grades courses:', courses);
    return courses;
  } catch (error) {
    console.log('❌ Error getting cached grades courses:', error);
    return [];
  }
};

/**
 * Get list of cached courses for attendance
 * @returns {Promise<Array>} - Array of cached course codes
 */
export const getCachedAttendanceCourses = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const attendanceKeys = keys.filter(key => 
      key.includes('attendance_cache_') && 
      !key.includes('timestamp') &&
      !key.includes('demo_') // Exclude demo data
    );
    
    const courses = attendanceKeys.map(key => {
      const parts = key.split('attendance_cache_');
      return parts[1]; // Extract course code
    }).filter(Boolean);
    
    console.log('📅 Cached attendance courses:', courses);
    return courses;
  } catch (error) {
    console.log('❌ Error getting cached attendance courses:', error);
    return [];
  }
};

// Individual cache checking functions
const hasGradesCachedData = async () => {
  const courses = await getCachedGradesCourses();
  return courses.length > 0;
};

const hasAttendanceCachedData = async () => {
  const courses = await getCachedAttendanceCourses();
  return courses.length > 0;
};

const hasScheduleCachedData = async () => {
  try {
    const cachedData = await AsyncStorage.getItem('schedule_cache');
    return !!cachedData;
  } catch (error) {
    return false;
  }
};

const hasExamsCachedData = async () => {
  try {
    const cachedData = await AsyncStorage.getItem('exams_cache');
    return !!cachedData;
  } catch (error) {
    return false;
  }
};

const hasTranscriptCachedData = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const transcriptKeys = keys.filter(key => 
      key.includes('transcript_cache_') && 
      !key.includes('demo_')
    );
    return transcriptKeys.length > 0;
  } catch (error) {
    return false;
  }
};

const hasFinancialsCachedData = async () => {
  try {
    const cachedData = await AsyncStorage.getItem('financials_cache');
    return !!cachedData;
  } catch (error) {
    return false;
  }
};

const hasNotificationsCachedData = async () => {
  try {
    const cachedData = await AsyncStorage.getItem('notifications_cache');
    return !!cachedData;
  } catch (error) {
    return false;
  }
};

const hasStudentInfoCachedData = async () => {
  try {
    const fullName = await AsyncStorage.getItem('student_fullName');
    const studentId = await AsyncStorage.getItem('student_id');
    const faculty = await AsyncStorage.getItem('student_faculty');
    
    return !!(fullName || studentId || faculty);
  } catch (error) {
    return false;
  }
};

/**
 * Show offline mode unavailable popup
 * @param {Function} showAlert - Alert function to display popup
 * @param {string} featureName - Name of the feature that's unavailable
 */
export const showOfflineUnavailablePopup = (showAlert, featureName = 'feature') => {
  showAlert(
    'Feature Unavailable',
    `${featureName} is not available in offline mode. Please go online to access this feature.`,
    [{ text: 'OK', style: 'default' }]
  );
};

/**
 * Check if user should be prompted for offline mode due to connection issues
 * @param {boolean} hasConnection - Whether device has internet connection
 * @param {boolean} isWebViewError - Whether there was a WebView error
 * @param {boolean} isTimeout - Whether there was a timeout
 * @returns {boolean} - Whether to show offline choice
 */
export const shouldShowOfflineChoice = (hasConnection, isWebViewError, isTimeout) => {
  // Show offline choice if:
  // 1. No internet connection, OR
  // 2. WebView error occurred, OR  
  // 3. Timeout occurred
  return !hasConnection || isWebViewError || isTimeout;
};

/**
 * Get offline choice reason based on conditions
 * @param {boolean} hasConnection - Whether device has internet connection
 * @param {boolean} isWebViewError - Whether there was a WebView error
 * @param {boolean} isTimeout - Whether there was a timeout
 * @returns {string} - Reason code for offline choice screen
 */
export const getOfflineChoiceReason = (hasConnection, isWebViewError, isTimeout) => {
  if (!hasConnection) return 'no_connection';
  if (isTimeout) return 'server_timeout';
  if (isWebViewError) return 'server_error';
  return 'connection_failed';
};
