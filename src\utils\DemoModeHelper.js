import AsyncStorage from '@react-native-async-storage/async-storage';
import { clearAllAppCaches } from './CacheManager';
import {
  DEMO_EXAMS_DATA,
  DEMO_SCHEDULE_DATA,
  DEMO_ATTENDANCE_DATA,
  DEMO_FINANCIALS_DATA,
  DEMO_TRANSCRIPT_DATA,
  DEMO_CUMULATIVE_GPA,
  DEMO_EVALUATE_COURSES,
  DEMO_EVALUATE_STAFF,
  DEMO_ACADEMIC_YEARS,
  DEMO_NOTIFICATIONS_DATA,
  DEMO_CMS_DATA,
  DEMO_COURSE_CONTENT,
  getDemoAttendanceDropdown,
  getDemoCMSDropdown,
  getDemoCourseContent
} from './DemoData';

/**
 * Check if the current user is in demo mode
 * @returns {Promise<boolean>}
 */
export const isDemoMode = async () => {
  try {
    const demoFlag = await AsyncStorage.getItem('is_demo_user');
    console.log('🔍 isDemoMode check: is_demo_user =', demoFlag, 'returning:', demoFlag === 'true');
    return demoFlag === 'true';
  } catch (error) {
    console.log('Error checking demo mode:', error);
    return false;
  }
};

/**
 * Simulate loading delay for demo mode
 * @param {number} delay - Delay in milliseconds (default: 1500)
 * @returns {Promise<void>}
 */
export const simulateLoading = (delay = 1500) => {
  return new Promise(resolve => setTimeout(resolve, delay));
};

/**
 * Get demo data for ExamsScreen
 * @returns {Array}
 */
export const getDemoExamsData = () => {
  return DEMO_EXAMS_DATA;
};

/**
 * Get demo data for ScheduleScreen
 * @returns {Array}
 */
export const getDemoScheduleData = () => {
  return DEMO_SCHEDULE_DATA;
};

/**
 * Get demo data for AttendanceScreen
 * @param {string} courseName - Selected course name
 * @returns {Object}
 */
export const getDemoAttendanceData = (courseName) => {
  return {
    dropdownOptions: getDemoAttendanceDropdown(),
    attendanceData: DEMO_ATTENDANCE_DATA[courseName] || [],
    attendanceWarnings: {
      totalAbsences: DEMO_ATTENDANCE_DATA[courseName] ? 
        DEMO_ATTENDANCE_DATA[courseName].filter(record => record.attendance === 'Absent').length : 0,
      warningLevel: 'safe' // safe, warning, danger
    }
  };
};

/**
 * Get demo data for FinancialsScreen
 * @returns {Array}
 */
export const getDemoFinancialsData = () => {
  return DEMO_FINANCIALS_DATA;
};

/**
 * Get demo data for TranscriptScreen
 * @returns {Object}
 */
export const getDemoTranscriptData = () => {
  return {
    transcriptData: DEMO_TRANSCRIPT_DATA,
    academicYears: DEMO_ACADEMIC_YEARS,
    cumulativeGPA: DEMO_CUMULATIVE_GPA,
    hasEvaluationOptions: false,
    evaluationOptions: []
  };
};

/**
 * Get demo data for EvaluateScreen
 * @returns {Object}
 */
export const getDemoEvaluateData = () => {
  return {
    courses: DEMO_EVALUATE_COURSES,
    staff: DEMO_EVALUATE_STAFF
  };
};

/**
 * Handle demo mode initialization for any screen
 * @param {Function} setLoading - Function to set loading state
 * @param {Function} setData - Function to set data
 * @param {any} demoData - Demo data to set
 * @param {number} delay - Loading delay (default: 1500)
 * @returns {Promise<boolean>} - Returns true if demo mode was handled
 */
export const handleDemoModeInit = async (setLoading, setData, demoData, delay = 1500) => {
  const isDemo = await isDemoMode();
  
  if (isDemo) {
    console.log('🎭 Demo mode detected - loading demo data');
    setLoading(true);
    
    await simulateLoading(delay);
    
    setData(demoData);
    setLoading(false);
    
    console.log('🎭 Demo data loaded');
    return true;
  }
  
  return false;
};

/**
 * Handle demo mode refresh for any screen
 * @param {Function} startAnimation - Function to start refresh animation
 * @param {Function} stopAnimation - Function to stop refresh animation
 * @param {number} delay - Refresh delay (default: 1500)
 * @returns {Promise<boolean>} - Returns true if demo mode was handled
 */
export const handleDemoModeRefresh = async (startAnimation, stopAnimation, delay = 1500) => {
  const isDemo = await isDemoMode();

  if (isDemo) {
    console.log('🎭 Demo mode - simulating refresh');
    startAnimation();

    await simulateLoading(delay);

    stopAnimation();
    console.log('🎭 Demo refresh complete');
    return true;
  }

  return false;
};

/**
 * Initialize demo mode or real mode with optional cache clearing
 * Call this when switching between demo and real accounts
 * @param {boolean} isDemoAccount - Whether switching to demo account
 * @param {boolean} shouldClearCaches - Whether to clear all caches (default: true)
 * @returns {Promise<void>}
 */
export const initializeAccountMode = async (isDemoAccount, shouldClearCaches = true) => {
  try {
    console.log(`🔄 initializeAccountMode called with isDemoAccount: ${isDemoAccount}, shouldClearCaches: ${shouldClearCaches}`);

    // Check current mode to determine if we're actually switching
    const currentDemoFlag = await AsyncStorage.getItem('is_demo_user');
    const currentIsDemoMode = currentDemoFlag === 'true';
    const isActuallySwitchingModes = currentIsDemoMode !== isDemoAccount;

    // Clear caches only if requested and we're actually switching modes or it's a fresh login
    if (shouldClearCaches && (isActuallySwitchingModes || currentDemoFlag === null)) {
      console.log('🗑️ Clearing all app caches...');
      await clearAllAppCaches();
    } else if (shouldClearCaches && !isActuallySwitchingModes) {
      console.log('🔄 Same mode detected, skipping cache clear to preserve data');
    } else {
      console.log('🔄 Cache clearing skipped (auto-login or mode preservation)');
    }

    // Set demo mode flag
    const flagValue = isDemoAccount ? 'true' : 'false';
    console.log(`🏳️ Setting is_demo_user flag to: ${flagValue}`);
    await AsyncStorage.setItem('is_demo_user', flagValue);

    // Verify the flag was set correctly
    const verifyFlag = await AsyncStorage.getItem('is_demo_user');
    console.log(`✅ Verification: is_demo_user flag is now: ${verifyFlag}`);

    console.log(`✅ Account mode initialized: ${isDemoAccount ? 'demo' : 'real'}`);
  } catch (error) {
    console.log('❌ Error initializing account mode:', error);
  }
};

// Demo mode initialization for Portal screen notifications
export const handleDemoPortalInit = async () => {
  console.log('🎭 handleDemoPortalInit called - loading demo notifications');
  console.log('🎭 DEMO_NOTIFICATIONS_DATA length:', DEMO_NOTIFICATIONS_DATA.length);
  console.log('🎭 First notification:', DEMO_NOTIFICATIONS_DATA[0]);
  return DEMO_NOTIFICATIONS_DATA;
};

// Demo mode refresh for Portal screen notifications
export const handleDemoPortalRefresh = async () => {
  console.log('🎭 Demo mode refresh - returning demo notifications');
  return DEMO_NOTIFICATIONS_DATA;
};

// Get demo CMS courses for a semester
export const getDemoCMSCourses = (semester) => {
  return DEMO_CMS_DATA[semester] || [];
};

// Get demo CMS semesters dropdown
export const getDemoCMSSemesters = () => {
  return Object.keys(DEMO_CMS_DATA).map(semester => ({
    value: semester,
    text: semester
  }));
};
