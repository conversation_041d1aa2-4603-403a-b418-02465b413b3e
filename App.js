import React, { useEffect } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Import theme provider
import { ThemeProvider } from './src/contexts/ThemeContext';
import { OfflineProvider } from './src/contexts/OfflineContext';

// Import debugging utilities
import DebugUtils from './src/utils/DebugUtils';
import ErrorBoundary from './src/components/ErrorBoundary';

// Import screens
import LoginScreen from './src/screens/LoginScreen';
import OfflineChoiceScreen from './src/screens/OfflineChoiceScreen';
import PortalScreen from './src/screens/Portal/PortalScreen';
import GradesScreen from './src/screens/Portal/GradesScreen';
import ExamsScreen from './src/screens/Portal/ExamsScreen';
import ScheduleScreen from './src/screens/Portal/ScheduleScreen';
import AttendanceScreen from './src/screens/Portal/AttendanceScreen';
import FinancialsScreen from './src/screens/Portal/FinancialsScreen';
import TranscriptScreen from './src/screens/Portal/TranscriptScreen';
import EvaluateScreen from './src/screens/Portal/EvaluateScreen';
import CMSScreen from './src/screens/CMS/CMSScreen';
import ContentScreen from './src/screens/CMS/ContentScreen';
import MailScreen from './src/screens/Mail/MailScreen';

const Stack = createStackNavigator();

// Main App Component with Debug Initialization
function App() {
  useEffect(() => {
    // Initialize debugging utilities
    DebugUtils.init();

    // Log app startup
    DebugUtils.log('info', 'App started', {
      timestamp: new Date().toISOString(),
      platform: require('react-native').Platform.OS
    });
  }, []);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <ErrorBoundary>
        <SafeAreaProvider>
          <OfflineProvider>
            <ThemeProvider>
          <NavigationContainer>
          <Stack.Navigator
            initialRouteName="Login"
            screenOptions={{
              headerStyle: {
                backgroundColor: '#1e3a8a', // GUC blue theme
              },
              headerTintColor: '#fff',
              headerTitleStyle: {
                fontWeight: 'bold',
              },
            }}
          >
          <Stack.Screen
            name="Login"
            component={LoginScreen}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="OfflineChoice"
            component={OfflineChoiceScreen}
            options={{
              headerShown: false,
              gestureEnabled: false
            }}
          />
          <Stack.Screen
            name="Portal"
            component={PortalScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: false // Disable swipe back gesture
            }}
          />
          <Stack.Screen
            name="Grades"
            component={GradesScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: false // Disable swipe back gesture - will use custom swipe for sidebar
            }}
          />
          <Stack.Screen
            name="Exams"
            component={ExamsScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: false // Disable swipe back gesture - will use custom swipe for sidebar
            }}
          />
          <Stack.Screen
            name="Schedule"
            component={ScheduleScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: false // Disable swipe back gesture - will use custom swipe for sidebar
            }}
          />
          <Stack.Screen
            name="Attendance"
            component={AttendanceScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: false // Disable swipe back gesture - will use custom swipe for sidebar
            }}
          />
          <Stack.Screen
            name="Financials"
            component={FinancialsScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: false // Disable swipe back gesture - will use custom swipe for sidebar
            }}
          />
          <Stack.Screen
            name="Transcript"
            component={TranscriptScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: false // Disable swipe back gesture - will use custom swipe for sidebar
            }}
          />
          <Stack.Screen
            name="Evaluate"
            component={EvaluateScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: false // Disable swipe back gesture - will use custom swipe for sidebar
            }}
          />
          <Stack.Screen
            name="CMS"
            component={CMSScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: true // Allow swipe back gesture
            }}
          />
          <Stack.Screen
            name="ContentScreen"
            component={ContentScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: true // Allow swipe back gesture
            }}
          />
          <Stack.Screen
            name="Mail"
            component={MailScreen}
            options={{
              headerShown: false, // Remove entire header
              gestureEnabled: true // Allow swipe back gesture
            }}
          />
        </Stack.Navigator>
        <StatusBar style="light" />
      </NavigationContainer>
      </ThemeProvider>
      </OfflineProvider>
    </SafeAreaProvider>
    </ErrorBoundary>
    </GestureHandlerRootView>
  );
}

export default App;
