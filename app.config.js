export default {
  expo: {
    name: "my<PERSON><PERSON>",
    slug: "my<PERSON><PERSON>", 
    version: "1.0.0",
    orientation: "portrait",
    icon: "./assets/myGUCLogomini.png",
    userInterfaceStyle: "light",
    newArchEnabled: false,
    codegenEnabled: false,
    splash: {
      image: "./assets/myGUCLogomini.png",
      resizeMode: "contain",
      backgroundColor: "#212121"
    },
    ios: {
      supportsTablet: true,
      bundleIdentifier: "com.myguc.app",
      icon: "./assets/myGUCLogomini1.png",
      splash: {
        image: "./assets/myGUCLogomini1.png",
        resizeMode: "contain",
        backgroundColor: "#212121"
      },
      deploymentTarget: "15.1",
      buildNumber: "1",
      newArchEnabled: false,
      infoPlist: {
        NSAppTransportSecurity: {
          NSAllowsArbitraryLoads: false,
          NSExceptionDomains: {
            "apps.guc.edu.eg": {
              NSExceptionAllowsInsecureHTTPLoads: false,
              NSExceptionMinimumTLSVersion: "1.0",
              NSExceptionRequiresForwardSecrecy: true,
              NSIncludesSubdomains: true
            },
            "cms.guc.edu.eg": {
              NSExceptionAllowsInsecureHTTPLoads: false,
              NSExceptionMinimumTLSVersion: "1.0",
              NSExceptionRequiresForwardSecrecy: true,
              NSIncludesSubdomains: true
            },
            "mail.guc.edu.eg": {
              NSExceptionAllowsInsecureHTTPLoads: false,
              NSExceptionMinimumTLSVersion: "1.0",
              NSExceptionRequiresForwardSecrecy: true,
              NSIncludesSubdomains: true
            },
            "u.expo.dev": {
              NSExceptionAllowsInsecureHTTPLoads: false,
              NSExceptionMinimumTLSVersion: "1.2",
              NSExceptionRequiresForwardSecrecy: true,
              NSIncludesSubdomains: true
            }
          }
        },
        CFBundleURLTypes: [
          {
            CFBundleURLName: "com.myguc.app",
            CFBundleURLSchemes: ["myguc"]
          }
        ]
      }
    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/myGUCLogomini.png",
        backgroundColor: "#212121"
      },
      package: "com.myguc.app",
      permissions: [
        "android.permission.INTERNET",
        "android.permission.ACCESS_NETWORK_STATE",
        "android.permission.CAMERA",
        "android.permission.READ_EXTERNAL_STORAGE",
        "android.permission.WRITE_EXTERNAL_STORAGE"
      ]
    },
    web: {
      favicon: "./assets/myGUCLogomini.png"
    },
    jsEngine: "hermes",
    plugins: [
      "expo-dev-client",
      [
        "expo-build-properties",
        {
          android: {
            enableProguardInReleaseBuilds: false,
            enableHermes: true,
            enableShrinkResourcesInReleaseBuilds: false,
            usesCleartextTraffic: true,
            minifyEnabled: false,
            shrinkResources: false,
            newArchEnabled: false,
            codegenEnabled: false
          },
          ios: {
            deploymentTarget: "15.1",
            useFrameworks: "static",
            enableBitcode: false,
            enableProguardInReleaseBuilds: false,
            enableShrinkResourcesInReleaseBuilds: false,
            minifyEnabled: false,
            newArchEnabled: false,
            hermesEnabled: true,
            codegenEnabled: false
          }
        }
      ]
    ],
    extra: {
      enableCrashReporting: true,
      enablePerformanceMonitoring: true,
      eas: {
        projectId: "77042771-6133-4af2-a0fd-dff0a0912017"
      }
    },
    // Development client configuration for standalone behavior
    developmentClient: {
      silentLaunch: true
    },
    updates: {
      url: "https://u.expo.dev/77042771-6133-4af2-a0fd-dff0a0912017"
    },
    runtimeVersion: {
      policy: "sdkVersion"
    }
  }
};
